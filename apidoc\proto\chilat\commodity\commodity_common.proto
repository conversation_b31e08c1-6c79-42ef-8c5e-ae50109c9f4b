syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity.common";

import "common.proto";
import "common/business.proto";

enum GoodsLookingState {
    GOODS_LOOKING_STATE_UNKNOWN = 0;
    GOODS_LOOKING_STATE_ASSIGN = 1; // 待分配
    GOODS_LOOKING_STATE_WAIT = 2; // 待下发
    GOODS_LOOKING_STATE_FINDING = 3; // 已下发
    GOODS_LOOKING_STATE_CONNECTED = 5; // 已建联
    GOODS_LOOKING_STATE_PREPARE_ORDER = 6; // 询运费中（待开单）
    GOODS_LOOKING_STATE_CREATED_ORDER = 8; // 已开单
    GOODS_LOOKING_STATE_CANCEL = 9; // 已取消

}

enum GoodsOnlineState {
    GOODS_ONLINE_STATE_UNKNOWN = 0; //查询条件，表示不限
    GOODS_ONLINE_STATE_ONLINE = 1; //上架状态
    GOODS_ONLINE_STATE_OFFLINE = 2; //下架状态
    GOODS_ONLINE_STATE_DRAFT = 3; //未发布状态
    GOODS_ONLINE_STATE_RECYCLE = 4; //已回收
    GOODS_ONLINE_STATE_PUBLISH_FAIL = 5; //发布失败
}

enum GoodsAttributeItemType {
    GOODS_ATTRIBUTE_ITEM_TYPE_UNKNOWN = 0;
    GOODS_ATTRIBUTE_ITEM_TYPE_INPUT = 10; // 输入框
    GOODS_ATTRIBUTE_ITEM_TYPE_MULTI_OPTION = 20; // 多选项
    GOODS_ATTRIBUTE_ITEM_TYPE_SELECT = 30; // 下拉框
}

// 库存操作类型
enum GoodsStockOperateType {
    GOODS_STOCK_OPERATE_TYPE_UNSPECIFIED = 0;
    GOODS_STOCK_OPERATE_TYPE_INCREASE = 1; //增加库存
    GOODS_STOCK_OPERATE_TYPE_REDUCE = 2; //减少库存
//    GOODS_STOCK_OPERATE_TYPE_IN_STOCK = 11; // 入库
//    GOODS_STOCK_OPERATE_TYPE_OUT_STOCK = 21; // 出库
//    GOODS_STOCK_OPERATE_TYPE_FREEZE = 12; // 冻结
//    GOODS_STOCK_OPERATE_TYPE_UNFREEZE = 22; // 解冻
}

// 商品来源类型
enum GoodsSourceType {
    GOODS_SOURCE_TYPE_UNSPECIFIED = 0;
    GOODS_SOURCE_TYPE_CRAWL = 1; // 人工抓取
    GOODS_SOURCE_TYPE_1688API = 2; // 1688同步
    GOODS_SOURCE_TYPE_PUBLISH = 3; // 人工创建
    GOODS_SOURCE_TYPE_IMAGE = 4; // 以图搜图
}

// 源商品状态
enum SourceGoodsState {
    SOURCE_GOODS_STATE_UNSPECIFIED = 0;
    SOURCE_GOODS_STATE_ACTIVE = 1; // 已生效
    SOURCE_GOODS_STATE_INACTIVE = 2; // 已失效
}

// 商品更新类型
enum GoodsUpdateType {
    GOODS_UPDATE_TYPE_UNSPECIFIED = 0;
    GOODS_UPDATE_TYPE_AUTO = 1; //程序自动更新（1688同步更新，1688同步创建商品时默认选中）
    GOODS_UPDATE_TYPE_MANUAL = 2; //手工更新
}

// 商品审核状态
enum GoodsAuditState {
    GOODS_AUDIT_STATE_UNSPECIFIED = 0;
    GOODS_AUDIT_STATE_TO_ASSIGN = 1; // 待分配
    GOODS_AUDIT_STATE_TO_AUDIT = 2; // 待校验
    GOODS_AUDIT_STATE_AUDITING = 3; // 校验中
    GOODS_AUDIT_STATE_FINISH = 4; // 校验完成
}

// 商品属性
message GoodsAttrModel {
    string attrId = 10; //属性ID
    string attrName = 20; //属性名称
    string attrValue = 25; //属性值（多个属性值用空格分隔）
    GoodsAttributeItemType attributeItemType = 30; //属性类型
    repeated string values = 40; //属性值（数组类型，支持多个）
}

// 商品信息选项
message GoodsExtendItem {
    string groupId = 1; // 字典ID
    string groupName = 2; // 字典名称（例：商品规格标题；商品属性名称）
    string groupNameCn = 3; // 字典名称中文
    string itemId = 4; // item id
    string itemName = 5; // item名称（例：商品规格名称；商品属性值）
    string itemNameCn = 6; // item名称中文
    string itemAlias = 7; // item别名（例：商品规格别名）
    string imageUrl = 8; // 图片链接（例：SKU图片）
    string color = 9; // 颜色值（格式：# + 6位16进制数，例：#CCCCCC）
    bool isActive = 10; // 是否启动，默认启用（例：商品属性的显示开关）
}

//商品线路运费（按“商品+国家+线路”配置）
message GoodsRouteFreightItem {
    string id = 10; // 商品线路运费ID
    string countryId = 20; //国家ID
    string countryName = 21; //国家名称（前端提交保存时，此参数忽略）
    string countryNameCn = 22; //国家中文名称（前端提交保存时，此参数忽略）
    string routeId = 30; //线路ID
    string routeName = 31; //线路名称（前端提交保存时，此参数忽略）
    string routeNameCn = 32; //线路中文名称（前端提交保存时，此参数忽略）
    double boxFreight = 40; //单箱运费
}

//装箱方案
message PackingSchemeItem {
    string id = 10; // 装箱方案ID
    int32 packingCount = 20; // 装箱数（表示每个包装箱中包含的商品件数，同一个商品或SKU中唯一）
    double boxLength = 30; // 包装箱的长（单位：cm）
    double boxWidth = 40; // 包装箱的宽（单位：cm）
    double boxHeight = 50; // 包装箱的高（单位：cm）
    double boxVolume = 60; // 单箱体积（单位：立方米，长*宽*高）
    double boxWeight = 70; // 单箱重量（单位：kg）
    repeated GoodsRouteFreightItem goodsFreightItems = 80; //商品线路运费配置（传null不更新；传空数组则删除原有配置）
    bool isToDelete = 90; // 是否为待删除（此字段传true时，有且仅有ID字段必填，后端将做删除操作）
}

// SKU信息
message SkuItem {
    string id = 1; // SKU ID（商品发布时，空值表示新增）
    string skuNo = 2; // SKU货号（由后端生成）
    repeated GoodsExtendItem specList = 3; // 规格信息（商品发布时，必填：groupName规格标题，itemName规格标题）
    //double salePrice = 4; // 销售价（必填）
    repeated SkuStepPriceAdmin stepPrices = 4; // sku阶梯价格（件数从少到多，价格从高到低）
    GoodsOnlineState skuState = 7; // 上架状态（由后端赋值）
    string skuStateDesc = 8; // 上架状态描述（由后端赋值）
    string sourceSkuId = 9; //源商品ID（1688同步时，附带）
    int32 amountOnSale = 10; // 库存
    string imageUrl = 11; // 图片链接
    string sourceSpecId = 12; // 1688SKU规格标识
    repeated PackingSchemeItem packingSchemes = 410; //装箱方案数组
    string fumengNo = 20; // 孚盟编号
}

// 商品操作日志查询参数
message GoodsOperationLogQueryParam {
    common.PageParam page = 1; //分页查询参数
    string id = 2; //数据ID（例：商品ID）
    bool querySkuLogFlag = 3; //在商品操作日志中，切换到“SKU日志”TAB时传值true（id参数仍为商品ID）
}

// 商品操作日志
message GoodsOperationLogResp {
    common.Result result = 1;
    common.Page page = 2;
    repeated GoodsOperationLogModel data = 3;
}

message GoodsOperationLogModel {
    string id = 1; //ID
    string operation = 2; //操作类型（新增、修改、删除或更详细的操作说明）
    string content = 3; //操作内容（html格式）
    string operator = 4; //操作人
    int64 operateTime = 5; //操作时间
}

// SKU阶梯价格（admin专用）
message SkuStepPriceAdmin {
    int32 start = 1; // 起始件数（第一个阶梯的start，总是从1开始）
    int32 end = 2; // 结束件数（-1表示无穷大；最后一个阶梯价的end，总是-1）
    double price = 3; // 阶梯价（最小售卖单位的价格，即商品价格单位对应的价格；第一个阶梯的salePrice总是与sku中的salePrice相同）
    double insideOnePrice = 4; // 内含单件商品的价格（与包装含量相关；chilat2.0中的包装含量总是1，因此insideOnePrice总是与stepPrice相等）
    double supplierPrice = 5; // 供应商价格（采购价；与阶梯价内含数量单位相同）
    double outSalePrice = 6;  // 外销价格（美元），不知道汇率、加点系数时填写
}
