<template>
  <div>
    <div class="whatsapp-wrapper" v-if="props.showWhatsAppCtaBanner">
      <div
        class="w-[6rem] text-[0.44rem] leading-[0.76rem] text-[#333] pt-[0.32rem] ml-[0.4rem] relative"
      >
        <span>Haga clic en</span>
        <span class="relative">
          <span class="text-[#25D366]"> WhatsApp</span>
          <img
            src="@/assets/icons/common/greenLine.svg"
            alt="line"
            class="w-[3.2rem] absolute top-[0.51rem] left-[0.22rem]"
            loading="lazy"
          />
        </span>
        <br />
        <span
          >para contactarnos y<br />
          obtener más información</span
        >
      </div>
      <img
        alt="click"
        class="icon"
        @click="onWhatsAppClick"
        src="@/assets/icons/common/mobileWhatsappClick.png"
        loading="lazy"
      />
      <n-button
        class="button"
        color="#E50113"
        @click="onWhatsAppClick"
        data-spm-box="potential_user_note_submit"
      >
        <div
          class="flex items-center justify-center text-[0.36rem] leading-[0.36rem] font-medium"
        >
          <img
            alt="WhatsApp"
            style="width: 0.88rem; margin-right: 0.08rem"
            src="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/12/12/41705bd3-e34c-4a9c-a8d4-2f8457029ad0.svg"
            loading="lazy"
          />
          Haga clic aquí
        </div>
      </n-button>
    </div>

    <!-- 联系信息区域 -->
    <div class="w-full py-[1rem] px-[0.4rem]">
      <!-- 办公室信息 -->
      <div class="flex items-start">
        <img
          src="@/assets/icons/common/address.svg"
          alt="address"
          class="w-[0.68rem] mr-[0.36rem]"
          loading="lazy"
        />
        <div>
          <div class="text-[0.4rem] leading-[0.4rem]">Oficina en China:</div>
          <p
            class="text-[0.32rem] leading-[0.4rem] text-[#7F7F7F] mt-[0.32rem]"
          >
            Room 2001, Building A, Liandu Mansion, Financial Business District,
            Yiwu, Zhejiang 322000, China
          </p>
          <p
            class="text-[0.32rem] leading-[0.4rem] text-[#7F7F7F] mt-[0.12rem]"
          >
            707#, Jinke Building, No. 19-2 Guangwei Road, Yuexiu District,
            Guangzhou, China
          </p>
        </div>
      </div>

      <!-- 邮箱联系方式 -->
      <!-- <a
        href="mailto:<EMAIL>"
        class="email-contact flex items-start mt-[0.8rem] mb-[1.28rem]"
      >
        <img
          src="@/assets/icons/common/email.svg"
          alt="Email"
          class="w-[0.68rem] mr-[0.36rem]"
          loading="lazy"
        />
        <div>
          <div class="text-[0.4rem] leading-[0.4rem]">Email:</div>
          <p
            class="text-[0.32rem] leading-[0.4rem] text-[#7F7F7F] mt-[0.32rem]"
          >
            <EMAIL>
          </p>
        </div>
      </a> -->

      <!-- WhatsApp联系方式 -->
      <a
        href="javascript:void(0)"
        @click="onWhatsAppClick"
        class="whatsapp-contact flex items-start mt-[0.8rem]"
      >
        <img
          src="@/assets/icons/common/whatsapp.svg"
          alt="WhatsApp"
          class="w-[0.68rem] mr-[0.36rem]"
          loading="lazy"
        />
        <div>
          <div class="text-[0.4rem] leading-[0.4rem]">
            Contáctanos por WhatsApp:
          </div>
          <p
            class="text-[0.32rem] leading-[0.4rem] text-[#7F7F7F] mt-[0.32rem]"
          >
            Haz clic para chatear con nuestro equipo y recibe asesoría inmediata
            para tus importaciones desde China.
          </p>
        </div>
      </a>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  showWhatsAppCtaBanner: {
    type: Boolean,
    default: true,
  },
});
</script>

<style scoped lang="scss">
.whatsapp-wrapper {
  position: relative;
  width: 100%;
  height: 8.68rem;
  background-size: 100%100%;
  color: #fff;
  background-image: url("@/assets/icons/common/mobileWhatsappBg.png");
  .icon {
    position: absolute;
    top: 2.8rem;
    width: 2.8rem;
    right: 0.32rem;
    z-index: 1;
  }
  .button {
    position: absolute;
    top: 4.64rem;
    right: 0.4rem;
    height: fit-content;
    display: inline-flex;
    padding: 0.32rem 0.68rem;
    align-items: center;
    gap: 0.08rem;
    border-radius: 12.96rem;
    border: 0.06rem solid var(---100, #fff);
    background: #25d366;
    box-shadow: 0 0.02rem 0.04rem 0 rgba(0, 0, 0, 0.5);
  }
}

.email-contact {
  &:hover {
    color: #e50113;

    img {
      content: url("@/assets/icons/common/email-highlight.svg");
    }

    div,
    p {
      color: #e50113;
    }
  }
}

.whatsapp-contact {
  &:hover {
    color: #25d366;

    img {
      content: url("@/assets/icons/common/whatsapp-highlight.svg");
    }

    div,
    p {
      color: #25d366;
    }
  }
}
</style>
