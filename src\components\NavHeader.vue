<template>
  <n-layout-header class="w-[1280px] mx-auto px-[38px] py-[28px]">
    <div class="flex items-center justify-between">
      <div class="logo" @click="onBackHome">
        <!-- <a href="/"> -->
        <img src="@/assets/icons/common/logo.png" alt="Chilat Logo" class="w-[150px]" loading="lazy" />
        <!-- </a> -->
      </div>
      <div class="mt-[10px] flex gap-[24px]">
        <n-dropdown
          size="medium"
          trigger="hover"
          :key="nav.key"
          v-for="nav in navData"
          placement="bottom-start"
          :options="nav.children"
          :render-label="renderDropdownLabel"
        >
          <div
            text
            @click="nav.type === 'text' ? '' : onMenuSelect(nav.key)"
            class="text-[16px] leading-[16px]"
            :class="
              nav.type === 'text' ? 'hover:text-[#7F7F7F]' : 'hover:text-[#E50113] hover:font-medium cursor-pointer'
            "
          >
            {{ nav?.label }}
          </div>
        </n-dropdown>
      </div>
      <n-button
        round
        size="large"
        color="#E50113"
        text-color="#fff"
        @click="onWhatsAppClick"
        class="w-[128px] h-[40px] text-center !hover:bg-[#D5141B]"
      >
        <span class="text-[16px] leading-[16px]">CONTACTO</span>
      </n-button>
    </div>
  </n-layout-header>
</template>
<script lang="ts" setup>
import type { VNodeChild } from "vue";
import hotIcon from "@/assets/icons/common/hot-icon.svg";
const router = useRouter();

interface DropdownOption {
  key: string;
  type?: string;
  label: VNodeChild;
  iconUrl?: string;
}

const navData = [
  {
    label: "NOSOTROS",
    key: "NOSOTROS",
    type: "text",
    children: [
      {
        label: "Quiénes somos",
        key: "/quienes-somos",
      },
      {
        label: "Cómo trabajarnos",
        key: "/como-trabajarnos",
      },
    ],
  },
  {
    label: "SERVICIOS",
    key: "/servicios",
    children: [
      {
        label: "Asesoría de compras",
        key: "/servicios#asesoria-de-compras",
      },
      {
        label: "Búsqueda de proveedores y productos",
        key: "/busqueda-de-proveedores-y-productos",
      },
      {
        label: "Validación de fabricantes",
        key: "/servicios#validacion-de-fabricantes",
      },
      {
        label: "Viajes de negocios a China",
        key: "/servicios#viajes-de-negocios-a-china",
      },
      {
        label: "Agente de compras",
        key: "/viajar-a-china",
      },
      {
        label: "Control de calidad",
        key: "/servicios#control-de-calidad",
      },
      {
        label: "Consolidación de cargas y logística",
        key: "/consolidacion-de-cargas-y-logistica",
      },
      {
        label: "Fotos del producto y catálogo",
        key: "/servicios#fotos-del-producto-y-catalogo",
      },
    ],
  },
  {
    label: "IMPORTACIÓN",
    key: "IMPORTACIÓN",
    type: "text",
    children: [
      {
        label: "Viajar a china",
        key: "/viajar-a-china",
      },
      {
        label: "Búsqueda de proveedores y productos",
        key: "/busqueda-de-proveedores-y-productos",
      },
      {
        iconUrl: hotIcon,
        label: "Selección online",
        key: "https://shop.chilat.com/",
      },
      {
        label: "Garantía de importación",
        key: "/servicio-de-garantia-de-importacion",
      },
    ],
  },
  {
    label: "FERIAS Y MERCADO",
    key: "FERIAS Y MERCADO",
    type: "text",
    children: [
      {
        label: "Cantón",
        key: "/canton",
        children: [
          {
            label: "Feria de Cantón",
            key: "/feria-de-canton",
          },
          {
            label: "Mercado mayorista en Guangzhou",
            key: "/mercado-de-guangzhou",
          },
          {
            label: "Mercado circundante de Guangzhou",
            key: "/mercado-circundante-de-guangzhou",
          },
        ],
      },
      {
        label: "Yiwu",
        key: "/donde-queda-yiwu/",
        children: [
          {
            label: "Viaja al mercado de yiwu",
            key: "/viaja-al-mercado-de-yiwu",
          },
          {
            label: "Las ventajas únicas del mercado de Yiwu",
            key: "/yiwu/",
          },
          {
            label: "Preguntas frecuentes sobre Yiwu",
            key: "/preguntas-frecuentes-sobre-yiwu",
          },
          {
            label: "Alojamiento y comida en Yiwu",
            key: "/alojamiento-y-comida-en-yiwu",
          },
          {
            label: "Guía del mercado de Yiwu",
            key: "/guia-del-mercado-de-yiwu",
          },
          {
            label: "Yiwu Fair",
            key: "/yiwu-fair",
          },
          {
            label: "Feria de Yiwu",
            key: "/feria-de-yiwu",
          },
        ],
      },
    ],
  },
  {
    label: "ELÍJANOS",
    key: "/elijanos",
    children: [
      {
        label: "Productos y calidad",
        key: "/elijanos#productos-y-calidad",
      },
      {
        label: "Consolidaciones y contenedor completo",
        key: "/elijanos#consolidaciones-y-contenedor-completo",
      },
      {
        label: "Acompañamiento y traducción",
        key: "/elijanos#acompanamiento-y-traduccion",
      },
    ],
  },
  {
    label: "SOCIO DE NEGOCIOS",
    key: "/socio-de-negocios",
  },
  {
    label: "BLOG",
    key: "/blog",
  },
];

function renderDropdownLabel(option: DropdownOption) {
  return h(
    "div",
    {
      class: "flex items-center gap-[8px]",
      onClick: () => {
        onMenuSelect(option.key);
      },
    },
    [
      h("span", {}, [option.label as VNodeChild]),
      option.iconUrl && typeof option.iconUrl === "string" && option.iconUrl.includes(".")
        ? h("img", { class: "icon-wrapper", src: option.iconUrl })
        : null,
    ],
  );
}

function onMenuSelect(path: string) {
  if (path.startsWith("http")) {
    window.open(path, "_blank");
  } else {
    router.push(path);
  }
}

function onBackHome() {
  router.push("/");
  // navigateTo("/", {
  //   redirectCode: 301,
  // });
}
</script>

<style scoped lang="scss"></style>
