<template>
  <div>
    <div class="whatsapp-wrapper" v-if="props.showWhatsAppCtaBanner">
      <div
        class="w-[540px] text-[42px] leading-[84px] text-[#333] pt-[73px] pl-[40px] relative font-medium"
      >
        <span
          >Haga clic en <span style="color: #25d366"> WhatsApp </span> para
          contactarnos y obtener más información</span
        >
        <img
          src="@/assets/icons/common/greenLine.svg"
          alt="line"
          class="absolute top-[139px] right-[26px]"
          loading="lazy"
        />
      </div>
      <img
        alt="click"
        class="icon"
        @click="onWhatsAppClick()"
        src="@/assets/icons/common/whatsappClick.png"
        loading="lazy"
      />
      <n-button
        color="#E50113"
        @click="onWhatsAppClick()"
        data-spm-box="potential_user_note_submit"
        class="button"
      >
        <div
          class="flex items-center justify-center text-[34px] leading-[34px] font-semibold"
        >
          <img
            alt="WhatsApp"
            style="width: 84px; margin-right: 8px"
            src="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/12/12/41705bd3-e34c-4a9c-a8d4-2f8457029ad0.svg"
            loading="lazy"
          />
          Haga clic aquí
        </div>
      </n-button>
    </div>

    <!-- 联系信息区域 -->
    <div class="w-full py-[100px] px-[38px] flex">
      <!-- 办公室信息 -->
      <div class="flex items-start flex-1">
        <img
          src="@/assets/icons/common/address.svg"
          alt="address"
          class="w-[56px] mr-[26px]"
          loading="lazy"
        />
        <div>
          <div class="text-[34px] leading-[34px] font-medium">
            Oficina en China:
          </div>
          <p class="text-[16px] leading-[21px] text-[#7F7F7F] mt-[20px]">
            Room 2001, Building A, Liandu Mansion, Financial Business District,
            Yiwu, Zhejiang 322000, China
          </p>
          <p class="text-[16px] leading-[21px] text-[#7F7F7F] mt-[12px]">
            707#, Jinke Building, No. 19-2 Guangwei Road, Yuexiu District,
            Guangzhou, China
          </p>
        </div>
      </div>

      <div class="w-[1px] h-[110px] mx-[37px] bg-[#D9D9D9] mt-[20px]"></div>

      <!-- 邮箱联系方式 -->
      <!-- <a
        href="mailto:<EMAIL>"
        class="email-contact flex items-start w-[352px]"
      >
        <img
          src="@/assets/icons/common/email.svg"
          alt="Email"
          class="w-[56px] mr-[26px]"
          loading="lazy"
        />
        <div>
          <div class="text-[34px] leading-[34px] font-medium">Email:</div>
          <p class="text-[18px] leading-[24px] text-[#7F7F7F] mt-[20px]">
            <EMAIL>
          </p>
        </div>
      </a> -->

      <!-- <div class="w-[1px] h-[110px] mx-[37px] bg-[#D9D9D9] mt-[20px]"></div> -->

      <!-- WhatsApp联系方式 -->
      <a
        href="javascript:void(0)"
        @click="onWhatsAppClick"
        class="whatsapp-contact flex items-start flex-1"
      >
        <img
          src="@/assets/icons/common/whatsapp.svg"
          alt="WhatsApp"
          class="w-[56px] mr-[26px]"
          loading="lazy"
        />
        <div>
          <div class="text-[34px] leading-[34px] font-medium">
            Contáctanos por WhatsApp:
          </div>
          <p class="text-[16px] leading-[21px] text-[#7F7F7F] mt-[20px]">
            Haz clic para chatear con nuestro equipo y recibe asesoría inmediata
            para tus importaciones desde China.
          </p>
        </div>
      </a>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  showWhatsAppCtaBanner: {
    type: Boolean,
    default: true,
  },
});
</script>

<style scoped lang="scss">
.whatsapp-wrapper {
  position: relative;
  height: 573px;
  background-size: 100% 100%;
  color: #fff;
  background-image: url("@/assets/icons/common/whatsappBg.png");
  .icon {
    position: absolute;
    top: -73px;
    width: 268px;
    right: 16px;
    z-index: 1;
  }
  .button {
    position: absolute;
    top: 63px;
    right: 40px;
    height: fit-content;
    display: inline-flex;
    padding: 28px 64px;
    align-items: center;
    gap: 8px;
    border-radius: 1200px;
    border: 6px solid var(---100, #fff);
    background: #25d366;
    box-shadow: 0px 2px 3px 0px rgba(0, 0, 0, 0.5);
  }
}

.email-contact {
  &:hover {
    color: #e50113;

    img {
      content: url("@/assets/icons/common/email-highlight.svg");
    }

    div,
    p {
      color: #e50113;
    }
  }
}

.whatsapp-contact {
  &:hover {
    color: #25d366;
    img {
      content: url("@/assets/icons/common/whatsapp-highlight.svg");
    }

    div,
    p {
      color: #25d366;
    }
  }
}
</style>
