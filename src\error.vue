<template>
  <div>
    <div v-if="error?.statusCode === 404">
      <div v-if="isMobile">
        <div class="relative min-h-screen bg-[#c7151d] text-white overflow-hidden">
          <!-- 顶部浮动的 SVG 装饰 -->
          <img
            :src="h5TopLeft"
            alt="装饰图"
            class="absolute top-0 left-[51%] -translate-x-[51%] h-auto pointer-events-none select-none z-10"
          />

          <img
            :src="h5LowerRight"
            alt="装饰图"
            class="absolute top-[27%] left-[50%] h-auto pointer-events-none select-none"
          />

          <div class="container mx-auto py-12 relative mt-40">
            <div class="flex flex-col items-center px-8">
              <img :src="notFound" alt="装饰性SVG图形" />
            </div>
            <div class="text-center mt-[76px] px-8">
              <p class="text-xl md:text-xl text-white leading-loose">
                Lo sentimos, la página que intenta acceder no existe.
              </p>
            </div>
            <div class="flex flex-col justify-center px-4 mt-[76px]">
              <button class="bg-white text-[#E50113] text-3xl px-6 py-4 rounded-full w-full" @click="onBackHome">
                Volver al inicio
              </button>
            </div>
          </div>
        </div>
      </div>
      <div v-else>
        <div
          class="relative min-h-screen flex items-center justify-center overflow-hidden"
          :style="{ backgroundColor: '#c7151d' }"
        >
          <!-- 顶部浮动的 SVG 装饰 -->
          <img
            :src="pcTopLeft"
            alt="装饰图"
            class="absolute top-[16%] left-[22%] -translate-x-1/2 h-auto pointer-events-none select-none transform -rotate-25"
          />

          <img
            :src="pcLowerRight"
            alt="装饰图"
            class="absolute top-0 left-[60%] h-auto pointer-events-none select-none"
          />

          <div class="grid grid-cols-2 min-h-screen px-16">
            <!-- 404 -->
            <div class="flex items-center justify-center">
              <img :src="notFound" alt="装饰性SVG图形" />
            </div>

            <!-- 描述和返回首页 -->
            <div class="relative flex flex-col items-center justify-center text-center">
              <div class="text-white text-xl mb-6 text-base">Lo sentimos, la página que intenta acceder no existe.</div>

              <!-- 返回首页 -->
              <button
                class="mt-16 ml-48 bg-white text-[#E50113] text-3xl px-10 py-6 rounded-full shadow-lg hover:bg-gray-100 transition"
                @click="onBackHome"
              >
                Volver al inicio
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="error-wrapper">
      <img
        alt="chilat"
        loading="lazy"
        class="error-image"
        src="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/04/10/698a0418-31ed-49e9-b8fb-3ef4f14a0c5b.svg"
      />
      <div class="error-message">El sistema está en mantenimiento. Por favor, inténtelo de nuevo en cinco minutos.</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import pcTopLeft from "@/assets/icons/404/pc-top-left.svg";
import pcLowerRight from "@/assets/icons/404/pc-lower-right.svg";
import h5TopLeft from "@/assets/icons/404/h5-top-left.svg";
import h5LowerRight from "@/assets/icons/404/h5-lower-right.svg";
import notFound from "@/assets/icons/404/404.svg";
import backgroundLinePC from "@/assets/icons/404/background-line-pc.svg";

const props = defineProps({
  error: Object,
});

const route = useRoute();
const router = useRouter(); // Nuxt3 路由实例
const { isMobile } = useDevice();
const error: any = useError();

useHead({
  title:
    error?.statusCode === 404
      ? `404 - Página no encontrada - Chilat`
      : "El sistema está en mantenimiento. Por favor, inténtelo de nuevo en cinco minutos.",
});

onMounted(() => {
  // const url = new URL(window.location.href);
  // const reloadTimes = Number(url.searchParams.get("reload_times") || 0) + 1;
  // url.searchParams.set("reload_times", String(reloadTimes));
  // setTimeout(() => {
  //   window.location.href = url.toString();
  // }, 60000);
});

// 返回首页
function onBackHome() {
  router.push("/");
}

// 处理页面不存在错误
const onHandleNotFound = () => {
  if (isMobile) {
    navigateTo(`/h5/?notfound=${encodeURIComponent(route.fullPath)}`);
  } else {
    navigateTo(`/?notfound=${encodeURIComponent(route.fullPath)}`);
  }
};

// 根据错误类型进行不同处理
if (props.error?.statusCode === 404) {
  // 404错误 - 页面不存在
  // onHandleNotFound();
}
</script>

<style scoped lang="scss">
.error-wrapper {
  min-height: 100vh;
  padding: 20vh 20px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.error-image {
  width: 100%;
  max-width: 580px;
  height: auto;
}

.error-message {
  font-size: 24px;
  line-height: 1.6;
  margin-top: 40px;
  color: #333;
}

@media (max-width: 1280px) {
  .error-image {
    max-width: 435px;
  }

  .error-message {
    font-size: 18px;
    line-height: 30px;
  }
}

@media (max-width: 750px) {
  .error-wrapper {
    max-width: 435px;
    margin: 0 auto;
    justify-content: center;
    padding-top: 0;
  }

  .error-message {
    font-size: 18px;
    line-height: 32px;
  }
}
</style>
