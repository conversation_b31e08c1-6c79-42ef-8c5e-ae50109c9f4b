export default defineNuxtRouteMiddleware((to) => {
  // // console.log("to:", to);
  // // // console.log("from:", from);
  // console.log("==>>TODO 9991:", to);
  // console.log("==>>TODO 9992:", to.fullPath === "/");
  // // 已经是 / 或者结尾有 / 的不处理
  // if (to.fullPath === "/" || to.fullPath.endsWith("/")) {
  //   console.log("==>>TODO 9994:", to.fullPath);
  //   return;
  // }
  // // 排除 API 和静态资源（按需）
  // if (to.fullPath.startsWith("/api") || to.fullPath.includes(".")) {
  //   console.log("==>>TODO 9995:", to.fullPath);
  //   return;
  // }
  // // ✅ 动态路由前缀（手动配置）
  // const dynamicPrefixes = ["/blog", "/article", "/h5/blog", "/h5/article"]; // 可根据实际项目修改
  // if (dynamicPrefixes.some((prefix) => to.fullPath.startsWith(prefix + "/"))) {
  //   console.log("==>>TODO 9995:", to.fullPath);
  //   return;
  // }
  // // if (/(?<!\/)#/g.test(to.fullPath)) {
  // //   return navigateTo(to.fullPath.replace(/(?<!\/)#/g, "/#"), { redirectCode: 301 });
  // // }
  // const newPath = to.fullPath.replace(/\/?(\?|$)/, "/$1");
  // console.log("==>>TODO 9998:", newPath);
  // return navigateTo(newPath, { redirectCode: 301 });
});
