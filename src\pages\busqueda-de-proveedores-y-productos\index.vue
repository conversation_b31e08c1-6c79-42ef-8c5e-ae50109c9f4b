<template>
  <div class="w-full">
    <div class="w-[1280px] mx-auto px-[38px] pt-[36px] pb-[150px]">
      <div class="relative z-2 flex">
        <div class="mr-[42px]">
          <div class="w-[530px] text-[42px] leading-[50px] font-medium">Su asistente de compras personalizado VIP</div>
          <div class="w-[530px] text-[24px] leading-[36px] text-[#7F7F7F] mt-[24px]">
            ¿Cómo encontrar proveedores confiables y conseguir precios competitivos de productos sin viajar a China?
            CHILAT se encarga de todo el proceso, desde la búsqueda de proveedores hasta el envío de sus productos.
          </div>
          <n-button
            size="large"
            color="#fff"
            text-color="#e50113"
            @click="onWhatsAppClick"
            class="global-navigation-btn mx-auto rounded-[500px] mt-[54px] h-[50px]"
          >
            <span class="text-[18px] leading-[18px] px-[38px]"> ¡Consultar ahora!</span>
          </n-button>
        </div>
        <div class="w-[633px] h-[356px] rounded-[4px] overflow-hidden">
          <video-you-tube
            :width="633"
            :height="356"
            :poster="vipCover"
            youtubeId="C09WtgdUp3M"
            titleCh="你的VIP订制找货助理"
          ></video-you-tube>
        </div>
      </div>
    </div>
    <div class="w-[1280px] mx-auto px-[38px] pb-[150px]">
      <div class="flex justify-between flex-wrap">
        <div
          v-for="(item, index) in vipIntroData"
          :key="index"
          class="relative w-[392px] h-[384px] bg-white border border-[#333] rounded-[20px] pt-[256px] px-[27px] text-center"
        >
          <img
            loading="lazy"
            :src="item.imgUrl"
            alt="busqueda de proveedores y productos"
            class="absolute top-0 left-0 w-full"
          />
          <div
            class="w-[54px] h-[54px] bg-[#e50113] text-[30px] leading-[54px] text-[#fff] absolute left-[50%] translate-x-[-50%] top-[185px] rounded-full border-1 border-[#fff]"
          >
            {{ index + 1 }}
          </div>
          <div class="text-[20px] leading-[24px]">
            {{ item.title }}
          </div>
          <div class="text-[16px] leading-[20px] text-[#7F7F7F] mt-[12px]">
            {{ item.desc }}
          </div>
        </div>
      </div>
      <div class="flex justify-center">
        <n-button
          size="large"
          color="#fff"
          text-color="#e50113"
          @click="onWhatsAppClick"
          class="global-navigation-btn mx-auto rounded-[500px] mt-[94px] h-[50px]"
        >
          <span class="text-[18px] leading-[18px] px-[38px]"> ¡Consultar ahora!</span>
        </n-button>
      </div>
    </div>
    <div class="w-[1280px] mx-auto px-[38px] pb-[200px]">
      <div class="text-[34px] leading-[34px] font-medium text-center">La importación solo toma cuatro pasos</div>
      <div class="w-[50px] h-[3px] bg-[#e50113] mx-auto mt-[36px]"></div>
      <div class="flex mx-auto justify-evenly mt-[110px]">
        <div v-for="(step, index) in vipProcData" :key="index" class="flex items-start">
          <div class="w-[240px] flex flex-col items-center text-center">
            <div class="w-[150px] h-[150px] flex items-center justify-center overflow-hidden">
              <img
                loading="lazy"
                :src="step.imgUrl"
                alt="busqueda de proveedores y productos"
                class="w-full h-full object-cover"
              />
              <div class="text-[28px] leading-[28px] font-bold text-[#e50113] italic absolute">
                {{ step.imgText }}
              </div>
            </div>
            <div class="text-[20px] leading-[24px] px-[10px] mt-[40px]">
              {{ step.title }}
            </div>
            <div class="text-[16px] leading-[20px] text-[#7F7F7F] mt-[14px]">
              {{ step.desc }}
            </div>
          </div>
          <n-image
            lazy
            v-if="index < vipProcData.length - 1"
            preview-disabled
            :src="stepArrow"
            class="mt-[75px] mr-[20px]"
          ></n-image>
        </div>
      </div>
      <div class="flex justify-center">
        <n-button
          size="large"
          color="#fff"
          text-color="#e50113"
          @click="onWhatsAppClick"
          class="global-navigation-btn mx-auto rounded-[500px] mt-[94px] h-[50px]"
        >
          <span class="text-[18px] leading-[18px] px-[38px]"> ¡Consultar ahora!</span>
        </n-button>
      </div>
    </div>
    <div class="bg-[#E50113] text-[#FFF] relative z-1">
      <div class="w-[1280px] mx-auto relative px-[38px] py-[80px]">
        <n-image
          lazy
          preview-disabled
          object-fit="fill"
          class="w-[688px] absolute right-[38px] top-[-50px]"
          src="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/12/06/aaf4cf98-ef26-4a1f-a873-7823ee8bac88.png"
        />
        <div class="text-[40px] leading-[48px] font-medium">¿Por qué nosotros?</div>
        <div class="w-[50px] h-[3px] bg-[#fff] mt-[36px]"></div>
        <n-button
          size="large"
          color="#e50113"
          text-color="#fff"
          @click="onWhatsAppClick"
          class="mx-auto rounded-[500px] mt-[36px] h-[50px] whatsapp-btn"
        >
          <span class="text-[18px] leading-[18px] px-[12px]"> Comience a abastecerse ahora</span>
        </n-button>
        <div class="flex justify-between mt-[112px]">
          <div v-for="(item, index) in whyUsData" :key="index" class="w-[250px] h-[262px] border-b-1 border-[#FFF]">
            <div class="flex text-[30px] leading-[30px] font-medium">
              <img loading="lazy" :src="rightArrowWhite" alt="busqueda de proveedores y productos" class="mr-[12px]" />
              0{{ index + 1 }}
            </div>
            <div class="min-h-[60px] text-[20px] leading-[20px] font-medium mt-[30px]" v-html="item.title"></div>
            <div class="text-[16px] leading-[20px] text-[#F2F2F2] mt-[20px]">
              {{ item.desc }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="w-[1280px] mx-auto px-[38px] py-[150px]">
      <div class="text-[42px] leading-[42px] text-center">
        <span style="font-size: 46px">10,000+</span> clientes confían en CHILAT
      </div>
      <div class="w-[50px] h-[3px] bg-[#e50113] mx-auto mt-[32px]"></div>
      <div class="text-[20px] leading-[34px] text-center mt-[18px] px-[125px] text-[#7F7F7F]">
        Tenemos clientes de diferentes industrias en toda América Latina. Escuche lo que comparten sobre su experiencia
        en importaciones.
      </div>
      <div class="flex justify-between flex-wrap mt-[66px]">
        <div v-for="video in userVideoData" :key="video.id" class="mt-[44px] relative" @click="onOpenVideo(video)">
          <img
            loading="lazy"
            :src="videoPlay"
            alt="busqueda de proveedores y productos"
            class="w-[70px] h-[48px] absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]"
          />
          <n-image lazy preview-disabled :src="video.poster" class="w-[388px] rounded-[4px]" />
        </div>
      </div>
    </div>

    <div class="w-[1280px] mx-auto">
      <WhatsAppContact />
    </div>
  </div>
  <video-modal ref="videoModalRef"></video-modal>
</template>

<script setup lang="ts">
import vipStep1 from "@/assets/icons/vip/vipStep1.svg";
import vipStep2 from "@/assets/icons/vip/vipStep2.svg";
import vipStep3 from "@/assets/icons/vip/vipStep3.svg";
import vipStep4 from "@/assets/icons/vip/vipStep4.svg";
import stepArrow from "@/assets/icons/vip/stepArrow.svg";
import videoPlay from "@/assets/icons/vip/videoPlay.svg";
import vipService from "@/assets/icons/vip/vipService.png";
import cargoConsolidation from "@/assets/icons/vip/cargoConsolidation.png";
import worryFree from "@/assets/icons/vip/worryFree.png";
import vipCover from "@/assets/icons/vip/vipCover.jpg";
import rightArrowWhite from "@/assets/icons/viajar-a-china/rightArrowWhite.svg";

import chileVideoPoster from "@/assets/icons/vip/chile-video-poster.jpg";
import hondurasVideoPoster from "@/assets/icons/vip/honduras-video-poster.jpg";
import argentinaVideoPoster from "@/assets/icons/vip/argentina-video-poster.jpg";
import argentinaVideoPoster2 from "@/assets/icons/vip/argentina-video-poster2.jpg";
import ecuadorVideoPoster from "@/assets/icons/vip/ecuador-video-poster.jpg";
import colombiaVideoPoster from "@/assets/icons/vip/colombia-video-poster.jpg";

useHead({
  title: "¿Cómo encontrar proveedores chinos para importar? Chilat te ayuda a importar de China a España",
  meta: [
    {
      name: "description",
      content:
        "Chilat es una marca muy conocida en el mercado de América Latina，Con más de 20 años de servicio dedicados al comercio entre China y América Latina, entendemos mejor sus necesidades.",
    },
  ],
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/busqueda-de-proveedores-y-productos/`,
    },
    {
      rel: "alternate",
      media: "only screen and (max-width: 640px)",
      href: `https://www.chilat.com/h5/busqueda-de-proveedores-y-productos/`,
    },
  ],
});

const videoModalRef = ref<any>(null);

const vipIntroData = [
  {
    imgUrl: vipService,
    title: "Servicio al cliente exclusivo VIP",
    desc: "Orientación profesional en importaciones de servicio al cliente uno a uno, haciendo que el proceso de importación sea cómodo.",
  },
  {
    imgUrl: cargoConsolidation,
    title: "Consolidación de mercancías",
    desc: "Compra mínima sugerida: US$ 10,000, consolidación multiproveedor y multicategoría, reduciendo su carga de inventario.",
  },
  {
    imgUrl: worryFree,
    title: "Compras sin preocupaciones",
    desc: "Como tercero, controlamos estrictamente la seguridad del pago y la calidad del producto, haciéndole sentir más tranquilo al realizar compras.",
  },
];

const vipProcData = [
  {
    imgUrl: vipStep1,
    imgText: "PASO 1",
    title: "Abastecimiento de productos",
    desc: "Sólo tiene que hacernos saber el producto que necesita, y nuestra especialista en búsqueda de productos le encontrará el proveedor y el producto más adecuados.",
  },
  {
    imgUrl: vipStep2,
    imgText: "PASO 2",
    title: "Confirmación de Muestras",
    desc: "Le proporcionaremos un presupuesto en un plazo de dos días laborables y le enviaremos muestras o fotos para su confirmación.",
  },
  {
    imgUrl: vipStep3,
    imgText: "PASO 3",
    title: "Seguimiento de la producción del producto",
    desc: "Después de confirmar los detalles del producto, organizaremos la colocación de pedidos a la fábrica y hacer un seguimiento del proceso de producción",
  },
  {
    imgUrl: vipStep4,
    imgText: "PASO 4",
    title: "Recepción de mercancias y envío",
    desc: "Una vez que los productos estén listos, los recibiremos, inspeccionaremos y organizaremos que la empresa logística los envíe a su dirección.",
  },
];

const userVideoData = [
  {
    id: "Tj0nrnhxgXw",
    poster: chileVideoPoster,
    titleCh: "1.我们的客户对我们的看法2",
  },
  {
    id: "_omi5a-pHkA",
    poster: hondurasVideoPoster,
    titleCh: "2.我们的客户对我们的看法6",
  },
  {
    id: "wOX-XcU4AYY",
    poster: argentinaVideoPoster,
    titleCh: "3.我们的客户对我们的看法9",
  },
  {
    id: "RCxVG3ibbDI",
    poster: argentinaVideoPoster2,
    titleCh: "4.我们的客户对我们的看法10",
  },
  {
    id: "yaB96K9r4nQ",
    poster: ecuadorVideoPoster,
    titleCh: "5.我们的客户对我们的看法11",
  },
  {
    id: "7toC6bZfRGs",
    poster: colombiaVideoPoster,
    titleCh: "6.我们的客户对我们的看法12",
  },
];

const whyUsData = [
  {
    title: "CHILAT es una marca muy conocida en el mercado de América Latina",
    desc: "Con más de 20 años de servicio dedicados al comercio entre China y América Latina, entendemos mejor sus necesidades.",
  },
  {
    title: "Equipo <br/>experimentado",
    desc: "Contamos con más de 50 empleados comerciales de habla hispana, el 80% de ellos con más de 3 años de servicio, lo que nos brinda una amplia experiencia.",
  },
  {
    title: "Abundantes recursos de las fabricas",
    desc: "Con más de 2000 proveedores principales, garantizamos una perfecta coordinación de la producción.",
  },
  {
    title: "Equipo de aliados en múltiples países en América Latina",
    desc: "Contamos con agencias asociadas en varios países de América Latina que pueden brindarle soluciones localizadas.",
  },
];

const pageData = reactive(<any>{});

function onOpenVideo(video: any) {
  if (videoModalRef.value) {
    videoModalRef.value.onOpenVideo(video);
  }
}
</script>

<style scoped lang="scss">
.page-container {
  min-width: 1280px;
  height: auto;
  min-height: 100vh;
  color: #333;
}
.whatsapp-btn {
  border: 1px solid #fff;
}
</style>
