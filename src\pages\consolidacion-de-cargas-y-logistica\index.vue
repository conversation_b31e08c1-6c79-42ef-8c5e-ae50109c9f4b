<template>
  <div class="w-[1280px] mx-auto bg-white">
    <div class="page-header text-[#fff] overflow-auto px-[48px] py-[24px] relative">
      <video
        autoplay
        loop
        muted
        playsinline
        :poster="poster"
        src="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/07/07/a7e55a1a-2caf-4b58-88c0-2700529fdd61.mp4"
        class="absolute top-0 left-0 w-full h-full object-cover z-0"
      ></video>
      <div class="absolute top-0 left-0 w-full h-full bg-black opacity-45 z-1"></div>
      <div class="relative z-2">
        <div class="flex items-center gap-[10px]">
          <div class="w-[12px] h-[12px] bg-[#E50113] rounded-full"></div>
          <div class="text-[16px] leading-[16px]">Servicio de Consolidación de Compras —— CHILAT</div>
        </div>
        <div class="w-[458px] text-[74px] leading-[82px] font-medium mt-[8px]">
          ¿Importas <span class="text-[66px]">desde</span> China?
        </div>
        <div class="mt-[11px] w-[478px] text-[28px] leading-[42px] font-medium">
          Hazlo más fácil y económico con nuestro servicio de
          <span class="text-[32px]">consolidación</span>.
        </div>
        <div class="w-[416px] text-[18px] leading-[30px] mt-[21px]">
          Ideal para quienes compran en
          <span>Alibaba, 1688, ferias como la de Cantón</span>
          o mercados como <span>Yiwu</span>.
        </div>
        <div
          @click="onWhatsAppClick"
          class="inline-block py-[22px] px-[26px] text-center cursor-pointer rounded-[50px] border-[1px] border-[#fff] text-[16px] leading-[16px] mt-[27px] hover:bg-[#fff] hover:text-[#e50113] transition-all duration-300"
        >
          Empezar a importar con Chilat
        </div>
      </div>
    </div>
    <div class="py-[150px]">
      <div class="text-[34px] leading-[34px] font-medium text-center">
        ¿Qué incluye nuestro servicio de consolidación?
      </div>
      <div class="w-[50px] h-[3px] bg-[#e50113] mx-auto mt-[36px]"></div>
      <div class="flex gap-[60px] mt-[78px] pr-[80px] pl-[54px]">
        <div class="flex-1 flex justify-center gap-[34px]">
          <Transition name="fade-slide" mode="out-in">
            <div :key="pageData.activeInclusions.title" class="flex justify-center gap-[34px] w-full">
              <div
                v-for="(item, index) in pageData.activeInclusions.children"
                :key="index"
                class="flex-1 max-w-[340px]"
              >
                <img
                  loading="lazy"
                  :src="item.icon"
                  alt="consolidacion de cargas y logistica"
                  :class="
                    pageData.activeInclusions.children.length === 1
                      ? 'rounded-tl-[80px] rounded-tr-[20px] rounded-br-[80px] rounded-bl-[1px]'
                      : 'rounded-tl-[37.647px] rounded-tr-[9.412px] rounded-br-[37.647px] rounded-bl-[0.471px]'
                  "
                />
                <div class="text-[20px] leading-[22px] font-medium mt-[30px]">
                  {{ item.title }}
                </div>
                <div
                  class="text-[#7f7f7f] mt-[12px]"
                  :class="
                    pageData.activeInclusions.children.length === 1
                      ? 'text-[16px] leading-[32px]'
                      : 'text-[16px] leading-[20px]'
                  "
                >
                  {{ item.desc }}
                </div>
              </div>
            </div>
          </Transition>
        </div>
        <div class="flex flex-col w-[538px] cursor-pointer">
          <div
            v-for="(item, index) in consolidationInclusions"
            :key="index"
            class="flex gap-[54px] p-[20px] border-b-1 border-[#F2F2F2] transition-colors duration-300"
            @mouseenter="handleServiceHover(item, index)"
          >
            <div
              class="text-[16px] leading-[16px] mt-[10px] transition-colors duration-300"
              :class="pageData.activeInclusions.title === item.title ? 'text-[#e50113]' : 'text-[#7F7F7F]'"
            >
              0{{ index + 1 }}
            </div>
            <div
              class="text-[18px] leading-[27px] transition-colors duration-300"
              :class="pageData.activeInclusions.title === item.title ? 'text-[#e50113]' : 'text-[#333]'"
            >
              {{ item.title }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="rounded-[20px] w-full h-[304px] text-[#fff] pt-[54px] text-center"
      :style="{
        backgroundImage: `url(${redBg})`,
        backgroundSize: '100% 100%',
      }"
    >
      <div class="text-[34px] leading-[34px] font-medium">Entonces necesitas algo más que un agente logístico.</div>
      <div class="text-[20px] leading-[20px] mt-[40px]">
        Necesitas un socio en China que te acompañe desde el pedido hasta la entrega.
      </div>
      <n-button
        size="large"
        color="#fff"
        text-color="#e50113"
        @click="onWhatsAppClick"
        class="w-[246px] h-[78px] rounded-[500px] mt-[44px] global-contact-btn"
      >
        <span class="text-[18px] leading-[18px] mr-[8px]">Consultar ahora</span>
        <img
          loading="lazy"
          alt="consolidacion de cargas y logistica"
          class="arrow-icon"
          src="@/assets/icons/quienes-somos/arrow-line.svg"
        />
      </n-button>
    </div>
    <div class="w-full px-[38px] py-[150px]">
      <div class="text-[34px] leading-[34px] font-medium text-center">¿A quién está dirigido este servicio?</div>
      <div class="text-[18px] leading-[22px] mt-[16px] text-[#7F7F7F] text-center">Este servicio es ideal para...</div>
      <div class="w-[50px] h-[3px] bg-[#e50113] mt-[36px] mx-auto"></div>
      <div class="flex justify-between flex-wrap mt-[78px] gap-y-[60px]">
        <div v-for="(item, index) in intendedUsers" :key="index" class="w-[572px] flex flex-col items-center">
          <img :src="item.icon" alt="consolidacion de cargas y logistica" class="w-[58px] h-[58px]" loading="lazy" />
          <div class="text-[20px] leading-[22px] mt-[16px] text-center px-[36px] min-h-[44px]">
            {{ item.title }}
          </div>
        </div>
      </div>
      <div class="flex justify-center mt-[94px]">
        <n-button
          size="large"
          color="#fff"
          text-color="#e50113"
          @click="onWhatsAppClick"
          class="global-navigation-btn w-[260px] rounded-[500px] h-[60px]"
        >
          <span class="text-[18px] leading-[18px]">Consultar ahora</span>
        </n-button>
      </div>
    </div>

    <!-- 滚动步骤导航区域 -->
    <div class="w-full pb-[150px]">
      <div class="text-[34px] leading-[34px] font-medium text-center mb-[16px]">¿Por qué elegir a Chilat?</div>
      <div class="w-[50px] h-[3px] bg-[#e50113] mx-auto mb-[80px]"></div>

      <div class="max-w-[1200px] mx-auto px-[40px]">
        <div class="flex">
          <!-- 左侧主图区域 -->
          <div
            class="w-[488px] h-[322px] sticky"
            :style="{
              top: getMainImagePosition() + 'px',
            }"
          >
            <Transition name="image-fade" mode="out-in">
              <img
                :key="currentStep"
                :src="whyChooseSteps[currentStep].image"
                alt="consolidacion de cargas y logistica"
                class="w-full h-[300px] object-cover rounded-[20px]"
                loading="lazy"
              />
            </Transition>
          </div>

          <!-- 中间步骤线区域 -->
          <div class="w-[50px] relative flex flex-col items-center min-h-full ml-[58px] mr-[28px]">
            <!-- 背景线 -->
            <div
              class="absolute left-1/2 transform -translate-x-1/2 w-[1px] bg-[#F2F2F2]"
              :style="{
                top: getStepPosition(0) + 25 + 'px',
                zIndex: 1,
                height: backgroundLineHeight + 'px',
              }"
            ></div>
            <!-- 进度线 -->
            <div
              class="absolute left-1/2 transform -translate-x-1/2 w-[1px] bg-[#E50113] transition-all duration-500 ease-out"
              :style="{
                top: getStepPosition(0) + 25 + 'px',
                height: progressLineHeight + 'px',
                zIndex: 2,
              }"
            ></div>

            <!-- 步骤点 -->
            <div
              v-for="(step, index) in whyChooseSteps"
              :key="index"
              :ref="(el) => (stepRefs[index] = el)"
              class="absolute"
              :style="{
                top: getStepPosition(index) + 'px',
                zIndex: 10,
              }"
            >
              <div class="relative w-[50px] h-[50px] z-20 bg-white rounded-full">
                <!-- SVG 进度圆圈 -->
                <svg class="absolute inset-0 w-[50px] h-[50px] transform -rotate-90" viewBox="0 0 50 50">
                  <!-- 背景圆圈 -->
                  <circle cx="25" cy="25" r="24" fill="none" stroke="#e5e5e5" stroke-width="1" />
                  <!-- 进度圆圈 -->
                  <circle
                    cx="25"
                    cy="25"
                    r="24"
                    fill="none"
                    :stroke="index <= currentStep ? '#e50113' : '#e5e5e5'"
                    stroke-width="1"
                    stroke-dasharray="150.8"
                    :stroke-dashoffset="index <= currentStep ? 0 : 150.8"
                    class="transition-all duration-500 ease-out"
                    :class="{
                      'step-circle-animate': index === currentStep && stepAnimations[index],
                    }"
                  />
                </svg>

                <!-- 步骤数字 -->
                <div
                  class="absolute inset-0 flex items-center justify-center text-[34px] leading-[34px] transition-colors duration-500"
                  :class="index === currentStep ? 'text-[#e50113]' : 'text-[#999]'"
                >
                  {{ index + 1 }}
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧内容区域 -->
          <div class="flex-1 flex flex-col">
            <div
              v-for="(step, index) in whyChooseSteps"
              :key="index"
              :ref="(el) => (contentRefs[index] = el)"
              class="step-content mb-[92px]"
              :class="{ active: index === currentStep }"
            >
              <h3
                class="text-[24px] leading-[20px] font-medium py-[24px] border-b-[1px] border-[#F2F2F2] mb-[20px]"
                :class="index > 0 ? 'border-t-[1px]' : ''"
              >
                {{ step.title }}
              </h3>
              <div class="space-y-[24px]">
                <div v-for="(item, itemIndex) in step.items" :key="itemIndex" class="flex items-start gap-[10px]">
                  <img
                    loading="lazy"
                    class="w-[30px]"
                    alt="consolidacion de cargas y logistica"
                    src="@/assets/icons/consolidacion-de-cargas-y-logistica/check.svg"
                  />
                  <div class="text-[22px] leading-[22px] mt-[4px]">
                    {{ item.subtitle }}
                    <span class="text-[16px] text-[#7F7F7F]" v-if="item.desc">{{ item.desc }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div
      class="rounded-[20px] w-full h-[304px] text-[#fff] pt-[50px] text-center mb-[150px]"
      :style="{
        backgroundImage: `url(${redBg})`,
        backgroundSize: '100% 100%',
      }"
    >
      <div class="text-[34px] leading-[34px] font-medium">¿Quieres importar sin complicaciones ni pérdidas?</div>
      <div class="text-[20px] leading-[20px] mt-[40px]">
        Contáctanos hoy y descubre cómo consolidar tus compras con eficiencia y tranquilidad.
      </div>
      <n-button
        size="large"
        color="#fff"
        text-color="#e50113"
        @click="onWhatsAppClick"
        class="w-[360px] h-[78px] rounded-[500px] mt-[44px] global-contact-btn"
      >
        <span class="text-[18px] leading-[18px] mr-[8px]">Empezar a importar con Chilat</span>
        <img
          loading="lazy"
          alt="consolidacion de cargas y logistica"
          class="arrow-icon"
          src="@/assets/icons/quienes-somos/arrow-line.svg"
        />
      </n-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import redBg from "@/assets/icons/quienes-somos/red-bg.png";
import poster from "@/assets/icons/consolidacion-de-cargas-y-logistica/poster.jpg";
import ecommerceBuyers from "@/assets/icons/consolidacion-de-cargas-y-logistica/ecommerce-buyers.svg";
import fairAttendees from "@/assets/icons/consolidacion-de-cargas-y-logistica/fair-attendees.svg";
import varietyOrders from "@/assets/icons/consolidacion-de-cargas-y-logistica/variety-orders.svg";
import fullService from "@/assets/icons/consolidacion-de-cargas-y-logistica/full-service.svg";
import streamlining from "@/assets/icons/consolidacion-de-cargas-y-logistica/streamlining.jpg";
import synchronization from "@/assets/icons/consolidacion-de-cargas-y-logistica/synchronization.jpg";
import monitoring from "@/assets/icons/consolidacion-de-cargas-y-logistica/monitoring.jpg";
import settlement from "@/assets/icons/consolidacion-de-cargas-y-logistica/settlement.jpg";
import inspection from "@/assets/icons/consolidacion-de-cargas-y-logistica/inspection.jpg";
import clearance from "@/assets/icons/consolidacion-de-cargas-y-logistica/clearance.jpg";
import instant from "@/assets/icons/consolidacion-de-cargas-y-logistica/instant.jpg";
import proven from "@/assets/icons/consolidacion-de-cargas-y-logistica/proven.jpg";

useHead({
  title: "Cómo encontrar una empresa china de servicios de integración de adquisiciones-Chilat",
  meta: [
    {
      name: "description",
      content:
        "chilat el agente de compras y asesoramiento más grande de China dedicado a servir a importadores de habla hispana.Brindamos a nuestros clientes un conjunto de soluciones para todas las importaciones desde China.",
    },
  ],
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/consolidacion-de-cargas-y-logistica/`,
    },
    {
      rel: "alternate",
      media: "only screen and (max-width: 640px)",
      href: `https://www.chilat.com/h5/consolidacion-de-cargas-y-logistica/`,
    },
  ],
});

// 滚动步骤数据
const whyChooseSteps = [
  {
    image: proven,
    title: "Tenemos una trayectoria sólida y comprobada",
    items: [
      {
        subtitle: "22 años de experiencia",
        desc: "en comercio con Latinoamérica",
      },
      {
        subtitle: "Oficinas físicas",
        desc: "en Yiwu y Guangzhou: Estamos en el epicentro de los proveedores",
      },
      {
        subtitle: "Equipo de +100 expertos bilingües (español-chino):",
        desc: "Atención personalizada y entendimiento cultural profundo",
      },
      {
        subtitle: "Manejamos TODO por ti:",
        desc: "logística, pagos, control de calidad y normativas aduaneras",
      },
    ],
  },
  {
    image: instant,
    title: "Usted puede obtener beneficios inmediatos",
    items: [
      {
        subtitle: "Ahorra hasta 40% en costos de envió",
      },
      {
        subtitle: "Reduce tiempo de gestión en 70%",
      },
      {
        subtitle: "Control de calidad garantizado",
      },
      {
        subtitle: "Documentación sin errores",
      },
    ],
  },
];

// 响应式数据
const currentStep = ref(0);
const progressLineHeight = ref(0);
const backgroundLineHeight = ref(0);
const stepRefs = ref([]);
const contentRefs = ref([]);
const stepAnimations = ref(new Array(whyChooseSteps.length).fill(false));
const stepGaps = ref([]);

// 获取步骤圆圈的位置
const getStepPosition = (index) => {
  if (index === 0) return LAYOUT_CONFIG.STEP_CIRCLE_OFFSET;

  let position = LAYOUT_CONFIG.STEP_CIRCLE_OFFSET;
  for (let i = 0; i < index; i++) {
    position += stepGaps.value[i] || LAYOUT_CONFIG.DEFAULT_STEP_GAP;
  }
  return position;
};

// 配置常量
const LAYOUT_CONFIG = {
  STEP_CIRCLE_OFFSET: 9, // 步骤圆圈偏移
  MAIN_IMAGE_OFFSET: 24, // 主图偏移
  CIRCLE_RADIUS: 25, // 圆圈半径
  MIN_STEP_GAP: 150, // 最小步骤间距
  DEFAULT_STEP_GAP: 400, // 默认步骤间距
  SCROLL_OFFSET: 240, // 滚动偏移量
};

// 获取主图的动态位置 - 始终相对基准线偏移24px
const getMainImagePosition = () => {
  // 服务端渲染时的安全检查
  if (typeof window === "undefined") {
    return 24; // 服务端渲染时返回基础偏移值
  }

  // 计算视口中心位置
  const viewportHeight = window.innerHeight;
  const imageHeight = 322; // 主图容器高度
  const viewportCenter = viewportHeight / 2;

  // 相对于基准线向下偏移24px，并居中显示
  return Math.max(24, viewportCenter - imageHeight / 2);
};

// 计算步骤间距 - 基于右侧内容的实际位置动态计算
const calculateStepGaps = () => {
  nextTick(() => {
    if (contentRefs.value.length === 0) return;

    const gaps = [];
    for (let i = 0; i < whyChooseSteps.length - 1; i++) {
      const gap = calculateSingleStepGap(i);
      gaps.push(gap);
    }
    stepGaps.value = gaps;
  });
};

// 计算单个步骤间距
const calculateSingleStepGap = (index) => {
  const currentContent = contentRefs.value[index];
  const nextContent = contentRefs.value[index + 1];

  if (!currentContent || !nextContent) {
    return LAYOUT_CONFIG.DEFAULT_STEP_GAP;
  }

  const currentRect = currentContent.getBoundingClientRect();
  const nextRect = nextContent.getBoundingClientRect();

  // 计算实际距离并确保最小间距
  const actualDistance = nextRect.top - currentRect.top + LAYOUT_CONFIG.MAIN_IMAGE_OFFSET;
  return Math.max(actualDistance, LAYOUT_CONFIG.MIN_STEP_GAP);
};

// 计算当前激活的步骤
const calculateActiveStep = () => {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const windowHeight = window.innerHeight;
  let activeStep = 0;

  for (let i = 0; i < contentRefs.value.length; i++) {
    const element = contentRefs.value[i];
    if (!element) continue;

    const rect = element.getBoundingClientRect();
    const elementTop = rect.top + scrollTop;
    const elementMiddle = elementTop + rect.height / 2 - LAYOUT_CONFIG.SCROLL_OFFSET;

    if (scrollTop + windowHeight / 2 >= elementMiddle) {
      activeStep = i;
    }
  }
  return activeStep;
};

// 触发步骤动画
const triggerStepAnimation = (activeStep, previousStep) => {
  if (activeStep !== previousStep && activeStep < stepAnimations.value.length) {
    stepAnimations.value[activeStep] = false;
    nextTick(() => {
      stepAnimations.value[activeStep] = true;
    });
  }
};

// 更新进度线和背景线
const updateProgressLines = (activeStep) => {
  // 计算进度线高度
  if (activeStep === 0) {
    progressLineHeight.value = 0;
  } else {
    const targetStepPosition = getStepPosition(activeStep);
    const firstStepPosition = getStepPosition(0);
    progressLineHeight.value = Math.max(0, targetStepPosition - firstStepPosition);
  }

  // 计算背景线高度
  const lastStepPosition = getStepPosition(whyChooseSteps.length - 1);
  const firstStepPosition = getStepPosition(0);
  backgroundLineHeight.value = Math.max(0, lastStepPosition - firstStepPosition);
};

// 滚动监听逻辑
const handleScroll = () => {
  if (!contentRefs.value.length || !stepRefs.value.length) return;

  const activeStep = calculateActiveStep();
  const previousStep = currentStep.value;
  currentStep.value = activeStep;

  triggerStepAnimation(activeStep, previousStep);

  updateProgressLines(activeStep);
};

// 窗口大小变化处理函数
const handleResize = () => {
  calculateStepGaps();
};

// 初始化函数
const initializeLayout = () => {
  try {
    calculateStepGaps();
    handleScroll();
  } catch (error) {
    console.warn("Layout initialization failed:", error);
  }
};

// 生命周期钩子
onMounted(() => {
  initializeLayout();
  window.addEventListener("scroll", handleScroll, { passive: true });
  window.addEventListener("resize", handleResize, { passive: true });
});

onUnmounted(() => {
  window.removeEventListener("scroll", handleScroll);
  window.removeEventListener("resize", handleResize);
});

const consolidationInclusions = [
  {
    title: "¿Compras de varios proveedores pero no sabes integrar todo en un solo envío?",
    children: [
      {
        icon: streamlining,
        title: "Consolidación logística",
        desc: "Recopilamos tu mercancía de múltiples proveedores y la consolidamos en un solo contenedor o LCL.",
      },
    ],
  },
  {
    title: "¿Cada proveedor te pide pagos separados y entregas por su cuenta?",
    children: [
      {
        icon: synchronization,
        title: "Coordinación de pedidos con múltiples proveedores",
        desc: "Realizamos los pedidos por ti, con cantidades y condiciones claras.",
      },
      {
        icon: monitoring,
        title: "Seguimiento de producción y entregas",
        desc: "Controlamos que cada proveedor cumpla plazos y requisitos.",
      },
      {
        icon: settlement,
        title: "Gestión de Pagos",
        desc: "Paga a Chilat una sola vez – nosotros nos encargamos de repartir los pagos a tus proveedores, con transparencia total y seguridad financiera.",
      },
    ],
  },
  {
    title: "¿Necesitas verificar la calidad, pero estás lejos de China?",
    children: [
      {
        icon: inspection,
        title: "Recepción y control de calidad",
        desc: "Verificamos la mercancía antes del embarque y reportamos anomalías.",
      },
    ],
  },
  {
    title: "¿Te preocupa que los documentos de exportación y trámites sean un caos?",
    children: [
      {
        icon: clearance,
        title: "Documentación y trámites de exportación",
        desc: "Emitimos facturas, packing list, y facilitamos tu proceso de importación.",
      },
    ],
  },
];

const intendedUsers = [
  {
    icon: ecommerceBuyers,
    title: "Compradores que utilizan Alibaba, 1688 u otras plataformas online",
  },
  {
    icon: fairAttendees,
    title: "Clientes que visitan ferias como Cantón o el mercado de Yiwu",
  },
  {
    icon: varietyOrders,
    title: "Empresas que realizan pedidos pequeños pero diversos",
  },
  {
    icon: fullService,
    title: "Importadores que necesitan asistencia completa desde China",
  },
];

const pageData = reactive({
  activeInclusions: consolidationInclusions[0],
});

const handleServiceHover = (item: any, index: number) => {
  pageData.activeInclusions = item;
};
</script>
<style scoped lang="scss">
.page-header {
  width: 1280px;
  height: 538px;
  margin: 0 auto;
  position: relative;
  background: rgba(0, 0, 0, 0.45);
}

// 过渡动画样式
.fade-slide-enter-active {
  transition: opacity 0.3s ease-in-out;
}

.fade-slide-leave-active {
  transition: none;
}

.fade-slide-enter-from {
  opacity: 0;
}

.fade-slide-leave-to {
  opacity: 0;
}

.fade-slide-enter-to,
.fade-slide-leave-from {
  opacity: 1;
}

// 图片切换动画
.image-fade-enter-active {
  transition: opacity 0.3s ease-in-out;
}

.image-fade-leave-active {
  transition: none;
}

.image-fade-enter-from {
  opacity: 0;
}

.image-fade-leave-to {
  opacity: 0;
}

.image-fade-enter-to,
.image-fade-leave-from {
  opacity: 1;
}

// 步骤内容样式
.step-content {
  transition: opacity 0.3s ease-in-out;
  // opacity: 0.6;
}

.step-content.active {
  // opacity: 1;
}

// 步骤圆圈动画
.step-circle-animate {
  animation: circleProgress 0.6s ease-out;
}

@keyframes circleProgress {
  0% {
    stroke-dashoffset: 150.8;
  }
  100% {
    stroke-dashoffset: 0;
  }
}
</style>
