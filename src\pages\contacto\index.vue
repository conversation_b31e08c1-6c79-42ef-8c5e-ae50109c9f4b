<template>
  <div class="w-[1280px] mx-auto bg-white">
    <div class="page-header text-[#fff] overflow-auto py-[50px] pl-[24px]">
      <Breadcrumb :items="breadcrumbItems" />
      <div class="text-[80px] leading-[80px] font-500 mt-[40px]">Contacto</div>
    </div>
    <div class="pt-[120px] px-[38px]">
      <div class="text-center text-[34px] leading-[34px] text-[#333]">Contacto</div>
      <div class="text-center text-[18px] leading-[21px] text-[#7F7F7F] pt-[18px]">
        <div>Un representante de Chilat estará en contacto dentro de las 48 horas para</div>
        <div>organizar una consulta inicial y guiarle a través del proceso.</div>
      </div>
      <div class="flex justify-center">
        <hr class="w-[50px] mt-[20px] border-t-3 border-[#e50113]" />
      </div>
    </div>
    <div class="flex pt-[78px] gap-[12px] px-[38px]">
      <div class="w-[342px] h-[886] text-black flex-shrink-0">
        <n-flex vertical>
          <div
            class="flex items-start bg-gray-100 rounded-tl-[40px] rounded-bl-[40px] rounded-br-[40px] px-[28px] py-[32px] mb-[26px]"
          >
            <img src="@/assets/icons/common/wallet.svg" alt="contacto" class="w-[32px] mr-[10px]" loading="lazy" />
            <div>
              <div class="text-[16px] text-[#333] leading-[22px]">Se recomienda que monto mínima</div>
              <div class="text-[16px] text-[#333] leading-[22px]">sea de US$ 10,000.</div>
            </div>
          </div>
          <div class="py-[62px] px-[28px] font-normal">
            <!-- 中国办事处 -->
            <div class="flex items-start">
              <img src="@/assets/icons/common/address.svg" alt="contacto" class="w-[32px] mr-[10px]" loading="lazy" />
              <div class="pt-[6px]">
                <div class="text-[20px] leading-[20px] text-[#333] font-medium">Oficina en China:</div>
              </div>
            </div>
            <div class="flex flex-wrap ml-[42px] text-[#7F7F7F] text-[16px] leading-[20px] font-400">
              Room 2001, Building A, Liandu Mansion, Financial Business District, Yiwu, Zhejiang 322000, China
            </div>
            <div class="flex flex-wrap ml-[42px] text-[#7F7F7F] text-[16px] leading-[20px] font-400 mt-[6px]">
              707#, Jinke Building, No. 19-2 Guangwei Road, Yuexiu District, Guangzhou, China
            </div>
            <hr class="w-full border-t border-gray-100 mt-[24px] mb-[45px]" />
            <!-- WhatsApp -->
            <div @click="onWhatsAppClick()" class="whatsapp-contact hover:cursor-pointer">
              <div class="flex items-start">
                <img
                  src="@/assets/icons/common/whatsapp.svg"
                  alt="contacto"
                  class="w-[32px] mr-[10px]"
                  loading="lazy"
                />
                <div class="pt-[6px]">
                  <div class="text-[20px] leading-[20px] text-[#333] font-medium">WhatsApp:</div>
                </div>
              </div>
              <div class="ml-[42px] text-[#7F7F7F] text-[14px] leading-[22px]">
                <div>Haz clic aquí para chatear con</div>
                <div>nosotros.</div>
              </div>
            </div>
            <hr class="w-full border-t border-gray-100 my-[40px]" />
            <!-- Email -->
            <div @click="onOpenMailClient" class="email-contact hover:cursor-pointer">
              <!-- <a href="mailto:<EMAIL>" class="email-contact flex items-start w-[352px]"> -->
              <div class="flex items-start">
                <img src="@/assets/icons/common/email.svg" alt="contacto" class="w-[32px] mr-[10px]" loading="lazy" />
                <div>
                  <div class="text-[20px] leading-[320px4px] font-medium">Email:</div>
                </div>
              </div>
              <div class="ml-[42px] text-[#7F7F7F] text-[14px] leading-[22px]">
                <div><EMAIL></div>
              </div>
              <!-- </a> -->
            </div>
            <hr class="w-full border-t border-gray-100 mt-[41px]" />
          </div>
          <!-- 侵权产品的询问 -->
          <div
            class="flex items-start bg-gray-100 rounded-tl-[40px] rounded-tr-[40px] rounded-bl-[40px] px-[28px] py-[32px] mb-[26px]"
          >
            <img
              src="@/assets/icons/common/inquire.svg"
              alt="contacto"
              class="w-[32px] mr-[10px] text-[#E50113]"
              loading="lazy"
            />
            <div>
              <div class="text-[16px] text-[#333] leading-[22px]">No aceptamos consultas de</div>
              <div class="text-[16px] text-[#333] leading-[22px]">productos infractores, gracias.</div>
            </div>
          </div>
        </n-flex>
      </div>
      <div class="w-[850px]">
        <div class="w-full rounded-[20px] border border-[#7F7F7F] pt-[28px] px-[64px] pb-[24px]">
          <n-form
            ref="editFormRef"
            :model="editForm"
            :rules="editRules"
            label-align="left"
            label-placement="left"
            label-width="auto"
            require-mark-placement="left"
          >
            <n-form-item
              label="Nombre"
              path="contactName"
              class="left-form-item"
              label-style="color: #333; font-weight: 500; font-size: 14px"
            >
              <n-input
                v-trim
                clearable
                maxlength="100"
                class="custom-input"
                @keydown.enter.prevent
                v-model:value="editForm.contactName"
                placeholder="Por favor, introduzca"
                @blur="onBlurEvent(editForm.contactName, 'Nombre')"
              />
            </n-form-item>
            <n-form-item
              class="left-form-item"
              label="País"
              path="countryId"
              label-style="color: #333; font-weight: 500; font-size: 14px"
            >
              <n-select
                filterable
                class="custom-input"
                value-field="id"
                label-field="countryEsName"
                v-model:value="editForm.countryId"
                :options="pageData.countryList"
                placeholder="Por favor, elija"
                @update:value="(value, option) => onSelectCountry(value, option)"
              />
            </n-form-item>
            <n-form-item
              label="Whatsapp"
              path="whatsapp"
              class="left-form-item"
              label-style="color: #333; font-weight: 500; font-size: 14px"
            >
              <div class="!w-10">
                <span v-if="pageData?.countryRegexps?.areaCode">{{ pageData.countryRegexps.areaCode }}</span>
                <span class="text-[#A6A6A6]" v-else>+000</span>
              </div>
              <n-divider vertical class="h-full" />
              <n-input
                v-trim
                clearable
                maxlength="64"
                class="custom-input"
                @keydown.enter.prevent
                v-model:value="editForm.whatsapp"
                placeholder="Por favor, introduzca"
                @blur="onBlurEvent(editForm.whatsapp, 'Whatsapp')"
              />
            </n-form-item>
            <n-form-item
              label="Correo electrónico"
              path="email"
              class="left-form-item"
              label-style="color: #333; font-weight: 500; font-size: 14px"
            >
              <n-input
                v-trim
                clearable
                v-model:value="editForm.email"
                placeholder="Por favor, introduzca"
                class="custom-input"
                @keydown.enter.prevent
                @blur="onBlurEvent(editForm.email, 'Correo electrónico')"
              />
            </n-form-item>
            <n-form-item
              class="top-form-item"
              label="¿Quién eres?"
              path="userType"
              label-placement="top"
              label-style="color: #333; font-weight: 500; font-size: 14px"
            >
              <n-radio-group
                v-model:value="editForm.userType"
                :on-update:value="(value) => onSelectEvent(value, 'userType', '¿Para qué está importando?')"
              >
                <n-flex>
                  <n-radio v-for="item in userTypeList" :value="item.value" :key="item.value" class="mt-[4px]">
                    <div class="flex">
                      <div>{{ item.label }}</div>
                    </div>
                  </n-radio>
                  <n-form-item
                    path="userTypeRemark"
                    class="inner-form-item"
                    v-if="editForm.userType === 'POTENTIAL_USER_TYPE_OTHER'"
                  >
                    <n-input
                      round
                      v-trim
                      clearable
                      maxlength="200"
                      @keydown.enter.prevent
                      v-model:value="editForm.userTypeRemark"
                      placeholder="Por favor, introduzca"
                    />
                  </n-form-item>
                </n-flex>
              </n-radio-group>
            </n-form-item>
            <n-form-item
              class="top-form-item"
              label="¿Tiene experiencia en importación?"
              path="withImportExperience"
              label-placement="top"
              label-style="color: #333; font-weight: 500; font-size: 14px"
            >
              <n-radio-group
                v-model:value="editForm.withImportExperience"
                :on-update:value="
                  (value) => onSelectEvent(value, 'withImportExperience', '¿Tiene experiencia en importación?')
                "
              >
                <n-space>
                  <n-radio v-for="item in withImportExperienceList" :value="item.value" :key="item.value">{{
                    item.label
                  }}</n-radio>
                </n-space>
              </n-radio-group>
            </n-form-item>
            <n-form-item
              class="top-form-item"
              path="purchaseQuantity"
              label="¿Cantidad?"
              label-placement="top"
              label-style="color: #333; font-weight: 500; font-size: 14px"
            >
              <n-radio-group
                v-model:value="editForm.purchaseQuantity"
                :on-update:value="(value) => onSelectEvent(value, 'purchaseQuantity', '¿Cantidad?')"
              >
                <n-space>
                  <n-radio v-for="item in purchaseQuantityList" :value="item.value" :key="item.value">{{
                    item.label
                  }}</n-radio>
                </n-space>
              </n-radio-group>
            </n-form-item>
            <n-form-item
              class="mt-[16px]"
              path="buyerRemark"
              label="Consulta"
              label-placement="top"
              label-style="color: #333; font-weight: 500; font-size: 14px"
            >
              <n-input
                class="rounded-[20px]"
                type="textarea"
                show-count
                :autosize="{
                  minRows: 3,
                  maxRows: 3,
                }"
                v-model:value="editForm.buyerRemark"
              >
              </n-input>
            </n-form-item>
          </n-form>
        </div>
        <div class="pt-[8px] pb-[42px] mt-[26px]">
          <n-button
            color="#E50113"
            @click="onPotentialUserSubmit"
            :loading="pageData.submitLoading"
            data-spm-box="potential_user_note_submit"
            class="w-full px-8 py-[22px] rounded-[20px] text-[18px] leading-[18px] font-bold"
          >
            <div class="flex items-center justify-center">Enviar</div>
          </n-button>
        </div>
      </div>
    </div>
    <!-- </div> -->
  </div>
</template>

<script setup lang="ts">
import type { FormInst, FormItemRule, FormRules } from "naive-ui";
import homeIcon from "@/assets/icons/common/home.svg";

useHead({
  title: "Contacto - Chilat",
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/contacto/`,
    },
    {
      rel: "alternate",
      media: "only screen and (max-width: 640px)",
      href: `https://www.chilat.com/h5/contacto/`,
    },
  ],
});

const pageData = reactive<any>({
  submitLoading: false,
});
const editForm = reactive<any>({
  countryId: null,
  contactName: null,
  whatsapp: null,
  email: null,
  userType: null,
  userTypeRemark: null,
  withImportExperience: null,
  purchaseQuantity: null,
  buyerRemark: null,
});
const editFormRef = ref<FormInst | null>(null);
const config = useRuntimeConfig();

await onGetCountry();

const userTypeList = [
  {
    value: "POTENTIAL_USER_TYPE_WHOLESALE",
    label: "Mayorista",
  },
  {
    value: "POTENTIAL_USER_TYPE_RETAIL",
    label: "Minoristas",
  },
  {
    value: "POTENTIAL_USER_TYPE_ONLINE_SHOP",
    label: "E-Commerce",
  },
  {
    value: "POTENTIAL_USER_TYPE_OTHER",
    label: "Otros",
  },
];

const purchaseQuantityList = [
  {
    value: "POTENTIAL_PURCHASE_QUANTITY_LCL",
    label: "Suelto",
  },
  {
    value: "POTENTIAL_PURCHASE_QUANTITY_FCL",
    label: "Por Contenedor",
  },
  {
    value: "POTENTIAL_PURCHASE_QUANTITY_NOT_SURE",
    label: "No sabe",
  },
];

const withImportExperienceList = [
  {
    value: "true",
    label: "Sí",
  },
  {
    value: "false",
    label: "No",
  },
];

const breadcrumbItems: any = [
  { link: "/", icon: homeIcon, alt: "home" },
  { link: "/contacto", text: "Contacto" },
];

// const editRules: any = {
const editRules: FormRules = {
  contactName: {
    required: true,
    trigger: "blur",
    message: "Por favor, introduzca",
    validator(_rule: FormItemRule, value: any) {
      return !!value?.trim();
    },
  },
  countryId: {
    required: true,
    trigger: "blur",
    message: "Por favor, elija",
    validator(_rule: FormItemRule, value: any) {
      return !!value?.trim();
    },
  },
  whatsapp: {
    required: true,
    trigger: "blur",
    message: "Por favor, introduzca",
    validator(rule: FormItemRule, value: any) {
      const lengths =
        pageData?.countryRegexps?.phoneCount && pageData?.countryRegexps?.phoneCount.split(",").map(Number);
      if (value && value.length && lengths && lengths.length > 0) {
        for (const length of lengths) {
          // 如果匹配任何一个长度，返回 true
          if (value.length === length) {
            return true;
          }
        }
      } else {
        if (value) {
          return true;
        }
      }
      return false;
    },
  },
  email: {
    required: true,
    trigger: "blur",
    message: "Introduzca el buzón correcto",
    validator(rule: FormItemRule, value: any, callback: Function) {
      const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      // 未填写邮箱
      if (!value) {
        return callback(new Error("Por favor, introduzca"));
      }
      // 格式不正确
      if (!pattern.test(value)) {
        return callback(new Error("Introduzca el buzón correcto"));
      }
      return callback();
    },
  },
  userType: {
    required: true,
    trigger: "blur",
    message: "Por favor, elija",
    validator(_rule: FormItemRule, value: any) {
      return !!value?.trim();
    },
  },
  withImportExperience: {
    required: true,
    trigger: "blur",
    message: "Por favor, elija",
    validator(_rule: FormItemRule, value: any) {
      return !!value?.trim();
    },
  },
  purchaseQuantity: {
    required: true,
    trigger: "blur",
    message: "Por favor, elija",
    validator(_rule: FormItemRule, value: any) {
      return !!value?.trim();
    },
  },
};

async function onGetCountry() {
  const res: any = await useGetCountry({});
  if (res?.result?.code === 200) {
    pageData.countryList = res?.data;
    if (config.public.defaultCountryCode) {
      res?.data.map((country: any) => {
        if (country.countryCodeTwo === config.public.defaultCountryCode) {
          editForm.countryId = country.id;
          pageData.countryRegexps = country;
          if (pageData.countryRegexps.phoneCount) {
            editRules[
              "whatsapp"
            ].message = `El número de teléfono debe ser un número de ${pageData.countryRegexps.phoneCount} dígitos y sin ningún otro carácter.`;
          }
        }
      });
    }
  }
}

function onSelectCountry(value: any, country: any) {
  window?.MyStat?.addPageEvent(
    "potential_user_select",
    `${country.countryEsName} 选择：${country?.countryName} （原值：${pageData?.countryRegexps?.countryName || "无"}）`,
  );
  pageData.countryRegexps = country;
  // 如果有长度校验 则校验长度
  if (pageData.countryRegexps.phoneCount) {
    editRules[
      "whatsapp"
    ].message = `El número de teléfono debe ser un número de ${pageData.countryRegexps.phoneCount} dígitos y sin ningún otro carácter.`;
  } else {
    // 没有长度校验 校验必填
    editRules["whatsapp"].message = `Por favor, introduzca WhatsApp`;
  }
}

function onOpenMailClient() {
  // 手动打开 mailto 链接
  window.location.href = "mailto:<EMAIL>";
}

// 下拉选择埋点事件
function onSelectEvent(value: string, attr: any, label: any) {
  editForm[attr] = value;
  let list = <any>[];
  if (attr === "userType") {
    list = userTypeList;
  }

  if (attr === "withImportExperience") {
    list = withImportExperienceList;
  }

  if (attr === "purchaseQuantity") {
    list = purchaseQuantityList;
  }

  const match = list.find((item: any) => item.value === value);
  if (!value) return;
  window?.MyStat?.addPageEvent("potential_user_select", `${label} 选择：${match?.label}`);
}

// 输入框埋点事件
async function onBlurEvent(value: string, label: any, form?: any) {
  window?.MyStat?.addPageEvent("potential_user_input", `${label} 输入：${value}`);
}

//
async function onPotentialUserSubmit() {
  try {
    const isValid = await editFormRef.value?.validate();
    if (isValid) {
      pageData.submitLoading = true;
      const res: any = await useSaveUserInfo(editForm);
      pageData.submitLoading = false;
      if (res?.result?.code === 200) {
        window?.MyStat?.addPageEvent("potential_user_submit_success", `保存潜客信息成功，顺序号：${res?.data?.seqNo}`);
        // TODO 待完善
        showToast("Mandar exitosa");
        setTimeout(() => {
          navigateTo(`/`);
        }, 1500);
      } else {
        showToast(res?.result?.message);
        window?.MyStat?.addPageEvent("potential_user_submit_error", `潜客信息表单错误：${res?.result?.message}`);
      }
    }
  } catch (error) {
    pageData.submitLoading = false;
  }
}
</script>

<style scoped lang="scss">
.page-header {
  width: calc(100% - 76px);
  height: 367px;
  margin: 0 auto;
  position: relative;
  object-fit: cover;
  background-size: 100%100%;
  background-image: url("@/assets/icons/contacto/header-bg.png");
  background-repeat: no-repeat;
}

.whatsapp-contact {
  &:hover {
    color: #25d366;

    img {
      content: url("@/assets/icons/common/whatsapp-highlight.svg");
    }

    div,
    p {
      color: #25d366;
    }
  }
}

.email-contact {
  &:hover {
    color: #e50113;

    img {
      content: url("@/assets/icons/common/email-highlight.svg");
    }

    div,
    p {
      color: #e50113;
    }
  }
}

.custom-input {
  border: none !important;
  --n-border: none !important;
  --n-border-warning: none;
  --n-border-focus-warning: none;
  --n-border-hover-warning: none;
  --n-border: none;
  --n-border-disabled: none;
  --n-border-hover: none;
  --n-border-focus: none;
  --n-box-shadow-focus: none;
  --n-box-shadow-focus-warning: none;
  --n-border-error: none;
  --n-border-focus-error: none;
  --n-border-hover-error: none;
  --n-box-shadow-focus-error: none;
  --n-border-active-error: none;
  --n-box-shadow-active: none;
  --n-border-active: none;
  :deep(.n-base-selection) {
    border: none !important;
    --n-border: none !important;
    --n-border-warning: none;
    --n-border-focus-warning: none;
    --n-border-hover-warning: none;
    --n-border: none;
    --n-border-disabled: none;
    --n-border-hover: none;
    --n-border-focus: none;
    --n-box-shadow-focus: none;
    --n-box-shadow-focus-warning: none;
    --n-border-error: none;
    --n-border-focus-error: none;
    --n-border-hover-error: none;
    --n-box-shadow-focus-error: none;
    --n-border-active-error: none;
    --n-box-shadow-active: none;
    --n-border-active: none;
  }
}

.left-form-item {
  padding-top: 30px;
  padding-bottom: 10px;
  width: 100%;
  background: #fff;
  position: relative;
  border-bottom: 1px solid #e6e6e6;
  overflow: visible;
  :deep(.n-form-item-feedback-wrapper) {
    position: absolute;
    bottom: -36px;
    left: -140px;
    color: #e50113;
    z-index: 2;
  }
}

.top-form-item {
  padding-top: 30px;
  padding-bottom: 10px;
  position: relative;
  border-bottom: 1px solid #e6e6e6;
  :deep(.n-form-item-feedback-wrapper) {
    position: absolute;
    bottom: -36px;
    left: 0;
    color: #e50113;
    z-index: 2;
  }
}
</style>
