<template>
  <div class="page-container">
    <div class="w-full px-[0.4rem] pt-[0.44rem] pb-[1.6rem]">
      <div class="w-[6.7rem] h-[3.76rem] rounded-[0.08rem] overflow-hidden mt-[0.3rem]">
        <video-you-tube
          :width="335"
          :height="188.438"
          :poster="vipCover"
          youtubeId="C09WtgdUp3M"
          titleCh="你的VIP订制找货助理"
        ></video-you-tube>
      </div>
      <div class="w-full text-[0.56rem] leading-[0.72rem] font-medium mt-[1rem]">
        Su asistente de compras personalizado VIP
      </div>
      <div class="w-full text-[0.32rem] leading-[0.48rem] text-[#7F7F7F] mt-[0.2rem]">
        ¿Cómo encontrar proveedores confiables y conseguir precios competitivos de productos sin viajar a China? CHILAT
        se encarga de todo el proceso, desde la búsqueda de proveedores hasta el envío de sus productos.
      </div>
      <div class="flex justify-center">
        <n-button
          size="large"
          color="#fff"
          text-color="#e50113"
          @click="onWhatsAppClick"
          class="global-navigation-btn mx-auto rounded-[10rem] mt-[1.2rem] h-[0.96rem]"
        >
          <span class="text-[0.32rem] leading-[0.4rem] px-[0.4rem]"> ¡Consultar ahora!</span>
        </n-button>
      </div>
    </div>
    <div class="w-full px-[0.4rem] pb-[1.6rem]">
      <div class="flex justify-between flex-wrap">
        <div
          v-for="(item, index) in vipIntroData"
          :key="index"
          class="relative w-[7.84rem] bg-white border border-[#333] rounded-[0.4rem] pt-[4.44rem] px-[0.28rem] pb-[0.52rem] text-center mb-[0.52rem]"
        >
          <img
            loading="lazy"
            :src="item.imgUrl"
            alt="busqueda de proveedores y productos"
            class="absolute top-0 left-0 w-full"
          />
          <div
            class="w-[1.08rem] h-[1.08rem] bg-[#e50113] text-[0.6rem] leading-[1.08rem] text-[#fff] absolute left-[50%] translate-x-[-50%] top-[3.16rem] rounded-full border-1 border-[#fff]"
          >
            {{ index + 1 }}
          </div>
          <div class="text-[0.4rem] leading-[0.48rem]">
            {{ item.title }}
          </div>
          <div class="text-[0.28rem] leading-[0.32rem] text-[#7F7F7F] mt-[0.2rem]">
            {{ item.desc }}
          </div>
        </div>
      </div>
      <div class="flex justify-center">
        <n-button
          size="large"
          color="#fff"
          text-color="#e50113"
          @click="onWhatsAppClick"
          class="global-navigation-btn mx-auto rounded-[10rem] mt-[1rem] h-[0.96rem]"
        >
          <span class="text-[0.32rem] leading-[0.4rem] px-[0.4rem]"> ¡Consultar ahora!</span>
        </n-button>
      </div>
    </div>
    <div class="w-full px-[0.4rem] pb-[3.08rem]">
      <div class="text-[0.68rem] leading-[0.68rem] font-medium text-center">La importación solo toma cuatro pasos</div>
      <div class="w-[1rem] h-[0.06rem] bg-[#e50113] mx-auto mt-[0.48rem]"></div>
      <div class="mt-[1rem] flex flex-col gap-[0.8rem]">
        <div
          v-for="(step, index) in vipProcData"
          :key="index"
          class="w-full flex flex-col items-center text-center px-[0.2rem]"
        >
          <div class="w-[1.92rem] h-[1.92rem] relative flex items-center justify-center">
            <img
              loading="lazy"
              :src="step.imgUrl"
              alt="busqueda de proveedores y productos"
              class="w-full h-full absolute top-0 left-0"
            />
            <div class="text-[0.36rem] leading-[0.36rem] text-[#e50113] italic font-bold relative">
              {{ step.imgText }}
            </div>
          </div>
          <div class="text-[0.36rem] leading-[0.36rem] mt-[0.4rem]">
            {{ step.title }}
          </div>
          <div class="text-[0.28rem] leading-[0.36rem] text-[#7F7F7F] mt-[0.2rem]">
            {{ step.desc }}
          </div>
        </div>
      </div>
      <div class="flex justify-center">
        <n-button
          size="large"
          color="#fff"
          text-color="#e50113"
          @click="onWhatsAppClick"
          class="global-navigation-btn mx-auto rounded-[10rem] mt-[1rem] h-[0.96rem]"
        >
          <span class="text-[0.32rem] leading-[0.4rem] px-[0.4rem]"> ¡Consultar ahora!</span>
        </n-button>
      </div>
    </div>
    <div class="bg-[#E50113] text-[#FFF] relative z-1">
      <div class="w-full relative px-[0.4rem] pt-[3.16rem] pb-[1.4rem]">
        <n-image
          lazy
          preview-disabled
          object-fit="fill"
          class="w-[6.7rem] absolute right-[0.4rem] top-[-1rem]"
          src="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/12/06/aaf4cf98-ef26-4a1f-a873-7823ee8bac88.png"
        />
        <div class="text-[0.56rem] leading-[0.68rem] text-center">¿Por qué nosotros?</div>
        <div class="w-[1rem] h-[0.06rem] bg-[#fff] mx-auto mt-[0.32rem]"></div>
        <div class="flex flex-col mt-[0.48rem]">
          <div
            v-for="(item, index) in whyUsData"
            :key="index"
            class="w-full pb-[0.52rem] border-b-1 border-[#FFF] mt-[0.52rem]"
          >
            <div class="flex text-[0.44rem] leading-[0.44rem] font-medium">
              <img
                loading="lazy"
                :src="rightArrowWhite"
                alt="busqueda de proveedores y productos"
                class="mr-[0.2rem] w-[0.16rem]"
              />
              0{{ index + 1 }}
            </div>
            <div class="text-[0.36rem] leading-[0.44rem] font-medium mt-[0.4rem]" v-html="item.title"></div>
            <div class="text-[0.28rem] leading-[0.36rem] text-[#F2F2F2] mt-[0.24rem]">
              {{ item.desc }}
            </div>
          </div>
        </div>
        <div class="flex justify-center">
          <n-button
            size="large"
            color="#e50113"
            text-color="#fff"
            @click="onWhatsAppClick"
            class="mx-auto rounded-[10rem] mt-[1rem] h-[0.96rem] whatsapp-btn"
          >
            <span class="text-[0.32rem] leading-[0.4rem] px-[0.4rem]"> Comience a abastecerse ahora</span>
          </n-button>
        </div>
      </div>
    </div>
    <div class="w-full px-[0.4rem] mt-[1.6rem]">
      <div class="text-[0.56rem] leading-[0.68rem] text-center font-medium">
        <span style="font-size: 0.6rem">10,000+</span> clientes confían en CHILAT
      </div>
      <div class="w-[1rem] h-[0.06rem] bg-[#e50113] mx-auto mt-[0.48rem]"></div>
      <div class="text-[0.32rem] leading-[0.4rem] text-center mt-[0.48rem] text-[#7F7F7F]">
        Tenemos clientes de diferentes industrias en toda América Latina. Escuche lo que comparten sobre su experiencia
        en importaciones.
      </div>
      <div class="w-full flex my-[1.2rem] pb-[3.1rem]">
        <n-carousel
          :space-between="10"
          :loop="false"
          draggable
          slides-per-view="auto"
          centered-slides
          show-arrow
          :on-update:current-index="onUpdateCurrentIndex"
        >
          <n-carousel-item
            class="!w-[5.76rem] !h-[3.24rem] rounded-[0.4rem] relative overflow-hidden flex-shrink-0"
            v-for="(video, index) in userVideoData"
            :key="video.id"
            @click="onOpenVideo(video)"
          >
            <div class="w-[5.76rem] h-[3.24rem]">
              <n-image lazy preview-disabled :src="video.poster" class="img" object-fit="cover" />
              <img
                loading="lazy"
                :src="videoPlay"
                alt="busqueda de proveedores y productos"
                class="w-[1rem] absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]"
              />
            </div>
          </n-carousel-item>
          <template #arrow="{ prev, next }">
            <div class="custom-arrow">
              <icon-card
                @click="prev"
                name="fe:arrow-left"
                size="26"
                :color="pageData.currentCarouselIndex === 0 ? '#D9D9D9' : '#e50113'"
                class="mr-[0.6rem]"
              >
              </icon-card>
              <icon-card
                @click="next"
                name="fe:arrow-right"
                size="26"
                :color="pageData.currentCarouselIndex === userVideoData.length - 1 ? '#D9D9D9' : '#e50113'"
              >
              </icon-card>
            </div>
          </template>
          <template #dots="{ total, currentIndex, to }">
            <ul class="custom-dots">
              <li v-for="index of total" :key="index" :class="{ ['is-active']: index - 1 <= currentIndex }"></li>
            </ul>
          </template>
        </n-carousel>
      </div>
    </div>
    <MobileWhatsAppContact />
  </div>
  <video-modal ref="videoModalRef"></video-modal>
</template>

<script setup lang="ts">
import vipStep1 from "@/assets/icons/vip/vipStep1.svg";
import vipStep2 from "@/assets/icons/vip/vipStep2.svg";
import vipStep3 from "@/assets/icons/vip/vipStep3.svg";
import vipStep4 from "@/assets/icons/vip/vipStep4.svg";
import videoPlay from "@/assets/icons/vip/videoPlay.svg";
import vipService from "@/assets/icons/vip/vipService.png";
import cargoConsolidation from "@/assets/icons/vip/cargoConsolidation.png";
import worryFree from "@/assets/icons/vip/worryFree.png";
import vipCover from "@/assets/icons/vip/vipCover.jpg";
import rightArrowWhite from "@/assets/icons/viajar-a-china/rightArrowWhite.svg";

import chileVideoPoster from "@/assets/icons/vip/chile-video-poster.jpg";
import hondurasVideoPoster from "@/assets/icons/vip/honduras-video-poster.jpg";
import argentinaVideoPoster from "@/assets/icons/vip/argentina-video-poster.jpg";
import argentinaVideoPoster2 from "@/assets/icons/vip/argentina-video-poster2.jpg";
import ecuadorVideoPoster from "@/assets/icons/vip/ecuador-video-poster.jpg";
import colombiaVideoPoster from "@/assets/icons/vip/colombia-video-poster.jpg";

useHead({
  title: "¿Cómo encontrar proveedores chinos para importar? Chilat te ayuda a importar de China a España",
  meta: [
    {
      name: "description",
      content:
        "Chilat es una marca muy conocida en el mercado de América Latina，Con más de 20 años de servicio dedicados al comercio entre China y América Latina, entendemos mejor sus necesidades.",
    },
  ],
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/busqueda-de-proveedores-y-productos/`,
    },
  ],
});

const videoModalRef = ref<any>(null);
const vipIntroData = [
  {
    imgUrl: vipService,
    title: "Servicio al cliente exclusivo VIP",
    desc: "Orientación profesional en importaciones de servicio al cliente uno a uno, haciendo que el proceso de importación sea cómodo.",
  },
  {
    imgUrl: cargoConsolidation,
    title: "Consolidación de mercancías",
    desc: "Compra mínima sugerida: US$ 10,000, consolidación multiproveedor y multicategoría, reduciendo su carga de inventario.",
  },
  {
    imgUrl: worryFree,
    title: "Compras sin preocupaciones",
    desc: "Como tercero, controlamos estrictamente la seguridad del pago y la calidad del producto, haciéndole sentir más tranquilo al realizar compras.",
  },
];

const vipProcData = [
  {
    imgUrl: vipStep1,
    imgText: "PASO 1",
    title: "Abastecimiento de productos",
    desc: "Sólo tiene que hacernos saber el producto que necesita, y nuestra especialista en búsqueda de productos le encontrará el proveedor y el producto más adecuados.",
  },
  {
    imgUrl: vipStep2,
    imgText: "PASO 2",
    title: "Confirmación de Muestras",
    desc: "Le proporcionaremos un presupuesto en un plazo de dos días laborables y le enviaremos muestras o fotos para su confirmación.",
  },
  {
    imgUrl: vipStep3,
    imgText: "PASO 3",
    title: "Seguimiento de la producción del producto",
    desc: "Después de confirmar los detalles del producto, organizaremos la colocación de pedidos a la fábrica y hacer un seguimiento del proceso de producción",
  },
  {
    imgUrl: vipStep4,
    imgText: "PASO 4",
    title: "Recepción de mercancias y envío",
    desc: "Una vez que los productos estén listos, los recibiremos, inspeccionaremos y organizaremos que la empresa logística los envíe a su dirección.",
  },
];

const userVideoData = [
  {
    id: "Tj0nrnhxgXw",
    poster: chileVideoPoster,
    titleCh: "1.我们的客户对我们的看法2",
  },
  {
    id: "_omi5a-pHkA",
    poster: hondurasVideoPoster,
    titleCh: "2.我们的客户对我们的看法6",
  },
  {
    id: "wOX-XcU4AYY",
    poster: argentinaVideoPoster,
    titleCh: "3.我们的客户对我们的看法9",
  },
  {
    id: "RCxVG3ibbDI",
    poster: argentinaVideoPoster2,
    titleCh: "4.我们的客户对我们的看法10",
  },
  {
    id: "yaB96K9r4nQ",
    poster: ecuadorVideoPoster,
    titleCh: "5.我们的客户对我们的看法11",
  },
  {
    id: "7toC6bZfRGs",
    poster: colombiaVideoPoster,
    titleCh: "6.我们的客户对我们的看法12",
  },
];

const whyUsData = [
  {
    title: "CHILAT es una marca muy conocida en el mercado de América Latina",
    desc: "Con más de 20 años de servicio dedicados al comercio entre China y América Latina, entendemos mejor sus necesidades.",
  },
  {
    title: "Equipo <br/>experimentado",
    desc: "Contamos con más de 50 empleados comerciales de habla hispana, el 80% de ellos con más de 3 años de servicio, lo que nos brinda una amplia experiencia.",
  },
  {
    title: "Abundantes recursos de las fabricas",
    desc: "Con más de 2000 proveedores principales, garantizamos una perfecta coordinación de la producción.",
  },
  {
    title: "Equipo de aliados en múltiples países en América Latina",
    desc: "Contamos con agencias asociadas en varios países de América Latina que pueden brindarle soluciones localizadas.",
  },
];

const pageData = reactive(<any>{
  currentCarouselIndex: 0,
});

function onOpenVideo(video: any) {
  if (videoModalRef.value) {
    videoModalRef.value.onOpenVideo(video);
  }
}

function onUpdateCurrentIndex(index: any) {
  pageData.currentCarouselIndex = index;
}
</script>

<style scoped lang="scss">
.page-container {
  height: auto;
  min-height: 100vh;
  color: #333;
}

.n-carousel {
  overflow: visible;
}
.custom-arrow {
  display: flex;
  position: absolute;
  bottom: -1.6rem;
  right: 0.2rem;
  z-index: 10;
}

.custom-dots {
  display: flex;
  margin: 0;
  padding: 0;
  position: absolute;
  bottom: -1.4rem;
  left: 0.4rem;
  border-radius: 0.08rem;
  background-color: #d9d9d9;
}

.custom-dots li {
  display: inline-block;
  width: 0.66rem;
  max-width: none;
  height: 0.04rem;
  background-color: #d9d9d9;
  border-radius: 0;
  cursor: pointer;
  margin: 0;
}

.custom-dots li.is-active {
  height: 0.06rem;
  background: #e50113;
}
.vjs-poster img {
  object-fit: none;
  border-radius: 0.24rem !important;
}
.whatsapp-btn {
  border: 0.02rem solid #fff;
}
</style>
