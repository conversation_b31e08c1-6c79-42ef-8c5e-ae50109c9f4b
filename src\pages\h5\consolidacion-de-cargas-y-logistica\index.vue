<template>
  <div class="w-full bg-white">
    <div class="page-header text-[#fff] overflow-auto px-[0.4rem] py-[0.6rem]">
      <video
        autoplay
        loop
        muted
        playsinline
        :poster="mobilePoster"
        src="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/07/07/92857e92-98dd-4573-9c13-1fe964e61798.mp4"
        class="absolute top-0 left-0 w-full h-full object-cover z-0"
      ></video>
      <div class="absolute top-0 left-0 w-full h-full bg-black opacity-45 z-1"></div>
      <div class="relative z-10">
        <div class="flex items-center gap-[0.16rem]">
          <div class="w-[0.2rem] h-[0.2rem] bg-[#E50113] rounded-full"></div>
          <div class="text-[0.28rem] leading-[0.28rem]">Servicio de Consolidación de Compras —— CHILAT</div>
        </div>
        <div class="text-[0.88rem] leading-[1.12rem] font-medium mt-[0.52rem]">
          ¿Importas<br />
          <span class="text-[0.72rem]">desde</span> China?
        </div>
        <div class="mt-[0.18rem] w-[5.6rem] text-[0.4rem] leading-[0.6rem]">
          Hazlo más fácil y económico con nuestro servicio de
          <span class="text-[0.44rem]">consolidación</span>.
        </div>
        <div class="text-[0.32rem] leading-[0.56rem] mt-[0.74rem]">
          Ideal para quienes compran en <br />
          Alibaba, 1688, ferias como la de Cantón <br />
          o mercados como Yiwu.
        </div>
        <div
          @click="onWhatsAppClick"
          class="inline-block py-[0.44rem] px-[0.36rem] text-center cursor-pointer rounded-[1rem] border-[0.03rem] border-[#fff] text-[0.32rem] leading-[0.32rem] mt-[0.78rem] hover:bg-[#fff] hover:text-[#e50113] transition-all duration-300"
        >
          Empezar a importar con Chilat
        </div>
      </div>
    </div>
    <div class="pt-[1.6rem] pb-[2rem] px-[0.4rem]">
      <div class="text-[0.56rem] leading-[0.68rem] text-center">¿Qué incluye nuestro servicio de consolidación?</div>
      <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mx-auto mt-[0.36rem]"></div>
      <div class="mt-[1rem] flex flex-col justify-between relative">
        <!-- 服务列表 -->
        <div
          v-for="(item, index) in consolidationInclusions"
          :key="item.title"
          class="service-item group"
          @click="handleServiceHover(item, index)"
        >
          <div
            class="py-[0.44rem] transition-colors duration-500 cursor-pointer border-b-1 border-[#F2F2F2]"
            :class="[pageData.activeInclusions.includes(item.title) ? 'text-[#e50113]' : '']"
          >
            <div class="flex items-start justify-between gap-[0.28rem]">
              <div class="flex">
                <span
                  class="text-[#999] text-[0.32rem] leading-[0.32rem] mr-[0.28rem] mt-[0.06rem]"
                  :class="[pageData.activeInclusions.includes(item.title) ? 'text-[#e50113]' : '']"
                  >{{ String(index + 1).padStart(2, "0") }}</span
                >
                <span class="text-[0.36rem] leading-[0.44rem]">{{ item.title }}</span>
              </div>
              <img
                v-if="!pageData.activeInclusions.includes(item.title)"
                loading="lazy"
                alt="consolidacion de cargas y logistica"
                class="w-[0.44rem] flex-shrink-0"
                src="@/assets/icons/common/expand-red.svg"
              />
              <img
                v-else
                loading="lazy"
                alt="consolidacion de cargas y logistica"
                class="w-[0.44rem] flex-shrink-0"
                src="@/assets/icons/common/collapse-red.svg"
              />
            </div>
            <!-- 展开的内容 -->
            <div
              class="w-full overflow-hidden transition-all duration-500 pl-[0.64rem]"
              :class="[
                pageData.activeInclusions.includes(item.title) ? 'mt-[0.2rem]  opacity-100' : 'max-h-0 opacity-0',
              ]"
            >
              <div class="flex flex-col w-full">
                <!-- 服务内容展示 -->
                <div v-for="(child, childIndex) in item.children" :key="childIndex" class="mt-[0.68rem]">
                  <div class="text-[0.36rem] leading-[0.4rem] font-medium text-[#333]">
                    {{ child.title }}
                  </div>
                  <div class="text-[0.32rem] leading-[0.48rem] mt-[0.12rem] text-[#333]">
                    {{ child.desc }}
                  </div>
                  <img
                    loading="lazy"
                    :src="child.icon"
                    alt="consolidacion de cargas y logistica"
                    class="w-[6.06rem] rounded-tl-[0.02rem] rounded-tr-[1.6rem] rounded-br-[0.4rem] rounded-bl-[1.6rem] mt-[0.2rem]"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="rounded-[0.4rem] w-full h-[4.64rem] text-[#fff] pt-[0.36rem] text-center px-[0.4rem]"
      :style="{
        backgroundImage: `url(${redBg})`,
        backgroundSize: '100% 100%',
      }"
    >
      <div class="text-[0.48rem] leading-[0.56rem] font-medium">
        Entonces necesitas algo más que un agente logístico.
      </div>
      <div class="text-[0.32rem] leading-[0.4rem] mt-[0.32rem]">
        Necesitas un socio en China que te acompañe desde el pedido hasta la entrega.
      </div>
      <n-button
        size="large"
        color="#fff"
        text-color="#e50113"
        @click="onWhatsAppClick"
        class="w-[4.12rem] h-[1.28rem] rounded-[10rem] mt-[0.36rem] global-contact-btn"
      >
        <span class="text-[0.32rem] leading-[0.32rem] mr-[0.12rem]">Consultar ahora</span>
        <img
          loading="lazy"
          alt="consolidacion de cargas y logistica"
          class="arrow-icon"
          src="@/assets/icons/quienes-somos/arrow-line.svg"
        />
      </n-button>
    </div>
    <div class="w-full px-[0.4rem] pt-[2rem] pb-[2.4rem]">
      <div class="text-[0.56rem] leading-[0.68rem]">¿A quién está dirigido este servicio?</div>
      <div class="text-[0.32rem] leading-[0.48rem] mt-[0.36rem] text-[#7F7F7F]">Este servicio es ideal para...</div>
      <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mt-[0.6rem]"></div>
      <div class="flex justify-between flex-wrap mt-[0.8rem] gap-y-[1rem]">
        <div v-for="(item, index) in intendedUsers" :key="index" class="flex flex-col items-center">
          <img
            :src="item.icon"
            alt="consolidacion de cargas y logistica"
            class="w-[0.76rem] h-[0.76rem]"
            loading="lazy"
          />
          <div class="text-[0.36rem] leading-[0.4rem] mt-[0.32rem] text-center">
            {{ item.title }}
          </div>
        </div>
      </div>
      <div class="flex justify-center mt-[1rem]">
        <n-button
          size="large"
          color="#fff"
          text-color="#e50113"
          @click="onWhatsAppClick"
          class="global-navigation-btn w-[4.12rem] rounded-[10rem] h-[1rem]"
        >
          <span class="text-[0.32rem] leading-[0.4rem]">Consultar ahora</span>
        </n-button>
      </div>
    </div>

    <!-- 滚动步骤导航区域 -->
    <div class="h5-why-choose-section w-full pb-[1.5rem] px-[0.4rem]">
      <div class="text-[0.56rem] leading-[0.68rem] text-center mb-[0.32rem]">¿Por qué elegir a Chilat?</div>
      <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mx-auto mt-[0.36rem]"></div>

      <div class="flex mt-[1rem] relative">
        <!-- 左侧连接线区域 -->
        <div class="h5-connection-container w-[0.76rem] mr-[0.24rem] absolute">
          <!-- 分段连接线 - 每两个圆圈之间一条线 -->
          <div
            v-for="(line, index) in h5ConnectionLines"
            :key="index"
            class="absolute left-1/2 transform -translate-x-1/2 z-1"
          >
            <!-- 背景线段 -->
            <div
              class="absolute w-[0.02rem] bg-[#F2F2F2] z-1"
              :style="{
                top: line.startY + 'px',
                height: line.height + 'px',
                zIndex: 1,
              }"
            ></div>
            <!-- 进度线段 -->
            <div
              class="absolute w-[0.02rem] bg-[#E50113] transition-all duration-500 ease-out"
              :style="{
                top: line.startY + 'px',
                height: line.height + 'px',
                zIndex: 2,
                opacity: index < h5CurrentStep ? 1 : 0,
                transform: index < h5CurrentStep ? 'scaleY(1)' : 'scaleY(0)',
                transformOrigin: 'top',
              }"
            ></div>
          </div>
        </div>

        <div class="flex-1 flex flex-col gap-[1rem]">
          <div
            v-for="(step, index) in whyChooseSteps"
            :key="index"
            :ref="(el) => (h5ContentRefs[index] = el)"
            class="step-content relative flex gap-[0.24rem]"
            :class="{ active: index === h5CurrentStep }"
          >
            <!-- 步骤圆圈 - 放在内容里 -->
            <div :ref="(el) => (h5StepRefs[index] = el)" class="relative z-10 mt-[0.5rem]">
              <div class="relative w-[0.76rem] h-[0.76rem] z-20 bg-white rounded-full">
                <!-- SVG 进度圆圈 -->
                <svg class="inset-0 w-[0.76rem] h-[0.76rem] transform -rotate-90" viewBox="0 0 38 38">
                  <!-- 背景圆圈 -->
                  <circle cx="19" cy="19" r="18" fill="none" stroke="#e5e5e5" stroke-width="1" />
                  <!-- 进度圆圈 -->
                  <circle
                    cx="19"
                    cy="19"
                    r="18"
                    fill="none"
                    :stroke="index <= h5CurrentStep ? '#e50113' : '#e5e5e5'"
                    stroke-width="1"
                    stroke-dasharray="150.8"
                    :stroke-dashoffset="index <= h5CurrentStep ? 0 : 150.8"
                    class="transition-all duration-500 ease-out"
                  />
                </svg>

                <div
                  class="absolute inset-0 flex items-center justify-center text-[0.52rem] leading-[0.52rem] transition-colors duration-500"
                  :class="index <= h5CurrentStep ? 'text-[#e50113]' : 'text-[#999]'"
                >
                  {{ index + 1 }}
                </div>
              </div>
            </div>
            <div>
              <h3
                class="text-[0.4rem] leading-[0.48rem] py-[0.4rem] border-b-[0.01rem] border-[#F2F2F2] mb-[0.52rem]"
                :class="[index > 0 ? 'border-t-[0.01rem]' : '']"
              >
                {{ step.title }}
              </h3>
              <div class="space-y-[0.4rem] mb-[0.4rem]">
                <div v-for="(item, itemIndex) in step.items" :key="itemIndex" class="flex items-start gap-[0.1rem]">
                  <img
                    loading="lazy"
                    class="w-[0.48rem]"
                    alt="consolidacion de cargas y logistica"
                    src="@/assets/icons/consolidacion-de-cargas-y-logistica/check.svg"
                  />
                  <div>
                    <div class="text-[0.36rem] leading-[0.36rem] mt-[0.06rem] mb-[0.12rem]">
                      {{ item.subtitle }}
                    </div>
                    <div
                      class="text-[0.32rem] leading-[0.44rem] text-[#7F7F7F] block"
                      v-if="'desc' in item && item.desc"
                    >
                      {{ item.desc }}
                    </div>
                  </div>
                </div>
              </div>
              <img
                :src="step.image"
                alt="consolidacion de cargas y logistica"
                class="w-full h-[3.8rem] object-cover rounded-[0.2rem]"
                loading="lazy"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <div
      class="rounded-[0.4rem] w-full h-[4.62rem] text-[#fff] pt-[0.32rem] text-center mb-[2.4rem]"
      :style="{
        backgroundImage: `url(${redBg})`,
        backgroundSize: '100% 100%',
      }"
    >
      <div class="text-[0.48rem] leading-[0.56rem] font-medium">¿Quieres importar sin complicaciones ni pérdidas?</div>
      <div class="text-[0.32rem] leading-[0.4rem] mt-[0.32rem]">
        Contáctanos hoy y descubre cómo consolidar tus compras con eficiencia y tranquilidad.
      </div>
      <n-button
        size="large"
        color="#fff"
        text-color="#e50113"
        @click="onWhatsAppClick"
        class="w-[6.16rem] h-[1.28rem] rounded-[10rem] mt-[0.36rem] global-contact-btn"
      >
        <span class="text-[0.32rem] leading-[0.32rem] mr-[0.12rem]">Empezar a importar con Chilat</span>
        <img
          loading="lazy"
          alt="consolidacion de cargas y logistica"
          class="arrow-icon w-[0.88rem] h-[0.88rem]"
          src="@/assets/icons/quienes-somos/arrow-line.svg"
        />
      </n-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import { reactive, onMounted, onUnmounted } from "vue";
import mobilePoster from "@/assets/icons/consolidacion-de-cargas-y-logistica/mobile-poster.jpg";
import redBg from "@/assets/icons/quienes-somos/mobile-red-bg.png";
import ecommerceBuyers from "@/assets/icons/consolidacion-de-cargas-y-logistica/ecommerce-buyers.svg";
import fairAttendees from "@/assets/icons/consolidacion-de-cargas-y-logistica/fair-attendees.svg";
import varietyOrders from "@/assets/icons/consolidacion-de-cargas-y-logistica/variety-orders.svg";
import fullService from "@/assets/icons/consolidacion-de-cargas-y-logistica/full-service.svg";
import streamlining from "@/assets/icons/consolidacion-de-cargas-y-logistica/streamlining.jpg";
import synchronization from "@/assets/icons/consolidacion-de-cargas-y-logistica/synchronization.jpg";
import monitoring from "@/assets/icons/consolidacion-de-cargas-y-logistica/monitoring.jpg";
import settlement from "@/assets/icons/consolidacion-de-cargas-y-logistica/settlement.jpg";
import inspection from "@/assets/icons/consolidacion-de-cargas-y-logistica/inspection.jpg";
import clearance from "@/assets/icons/consolidacion-de-cargas-y-logistica/clearance.jpg";
import instant from "@/assets/icons/consolidacion-de-cargas-y-logistica/instant.jpg";
import proven from "@/assets/icons/consolidacion-de-cargas-y-logistica/proven.jpg";

// 滚动步骤数据
const whyChooseSteps = [
  {
    image: proven,
    title: "Cómo encontrar una empresa china de servicios de integración de adquisiciones-Chilat",
    meta: [
      {
        name: "description",
        content:
          "chilat el agente de compras y asesoramiento más grande de China dedicado a servir a importadores de habla hispana.Brindamos a nuestros clientes un conjunto de soluciones para todas las importaciones desde China.",
      },
    ],
    items: [
      {
        subtitle: "22 años de experiencia",
        desc: "en comercio con Latinoamérica",
      },
      {
        subtitle: "Oficinas físicas",
        desc: "en Yiwu y Guangzhou: Estamos en el epicentro de los proveedores",
      },
      {
        subtitle: "Equipo de +100 expertos bilingües (español-chino):",
        desc: "Atención personalizada y entendimiento cultural profundo",
      },
      {
        subtitle: "Manejamos TODO por ti:",
        desc: "logística, pagos, control de calidad y normativas aduaneras",
      },
    ],
  },
  {
    image: instant,
    title: "Usted puede obtener beneficios inmediatos",
    items: [
      {
        subtitle: "Ahorra hasta 40% en costos de envió",
      },
      {
        subtitle: "Reduce tiempo de gestión en 70%",
      },
      {
        subtitle: "Control de calidad garantizado",
      },
      {
        subtitle: "Documentación sin errores",
      },
    ],
  },
];

useHead({
  title: "Cómo encontrar una empresa china de servicios de integración de adquisiciones-Chilat",
  meta: [
    {
      name: "description",
      content:
        "chilat el agente de compras y asesoramiento más grande de China dedicado a servir a importadores de habla hispana.Brindamos a nuestros clientes un conjunto de soluciones para todas las importaciones desde China.",
    },
  ],
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/consolidacion-de-cargas-y-logistica/`,
    },
  ],
});

const consolidationInclusions = [
  {
    title: "¿Compras de varios proveedores pero no sabes integrar todo en un solo envío?",
    children: [
      {
        icon: streamlining,
        title: "Consolidación logística",
        desc: "Recopilamos tu mercancía de múltiples proveedores y la consolidamos en un solo contenedor o LCL.",
      },
    ],
  },
  {
    title: "¿Cada proveedor te pide pagos separados y entregas por su cuenta?",
    children: [
      {
        icon: synchronization,
        title: "Coordinación de pedidos con múltiples proveedores",
        desc: "Realizamos los pedidos por ti, con cantidades y condiciones claras.",
      },
      {
        icon: monitoring,
        title: "Seguimiento de producción y entregas",
        desc: "Controlamos que cada proveedor cumpla plazos y requisitos.",
      },
      {
        icon: settlement,
        title: "Gestión de Pagos",
        desc: "Paga a Chilat una sola vez – nosotros nos encargamos de repartir los pagos a tus proveedores, con transparencia total y seguridad financiera.",
      },
    ],
  },
  {
    title: "¿Necesitas verificar la calidad, pero estás lejos de China?",
    children: [
      {
        icon: inspection,
        title: "Recepción y control de calidad",
        desc: "Verificamos la mercancía antes del embarque y reportamos anomalías.",
      },
    ],
  },
  {
    title: "¿Te preocupa que los documentos de exportación y trámites sean un caos?",
    children: [
      {
        icon: clearance,
        title: "Documentación y trámites de exportación",
        desc: "Emitimos facturas, packing list, y facilitamos tu proceso de importación.",
      },
    ],
  },
];

const intendedUsers = [
  {
    icon: ecommerceBuyers,
    title: "Compradores que utilizan Alibaba, 1688 u otras plataformas online",
  },
  {
    icon: fairAttendees,
    title: "Clientes que visitan ferias como Cantón o el mercado de Yiwu",
  },
  {
    icon: varietyOrders,
    title: "Empresas que realizan pedidos pequeños pero diversos",
  },
  {
    icon: fullService,
    title: "Importadores que necesitan asistencia completa desde China",
  },
];

const pageData = reactive({
  activeInclusions: [] as string[],
});

const h5CurrentStep = ref(0);
const h5StepRefs = ref<any[]>([]);
const h5ContentRefs = ref<any[]>([]);

// H5版本的连接线数据
const h5ConnectionLines = ref<Array<{ startY: number; height: number }>>([]);

// 计算H5分段连接线的位置
const calculateH5ConnectionLines = () => {
  nextTick(() => {
    if (h5StepRefs.value.length < 2) return;

    const lines: Array<{ startY: number; height: number }> = [];

    // 获取步骤内容元素来计算实际的布局间距
    for (let i = 0; i < h5ContentRefs.value.length - 1; i++) {
      const currentContent = h5ContentRefs.value[i];
      const nextContent = h5ContentRefs.value[i + 1];

      if (!currentContent || !nextContent) continue;

      const currentContentRect = currentContent.getBoundingClientRect();
      const nextContentRect = nextContent.getBoundingClientRect();

      // 获取连接线容器的位置作为基准
      const connectionContainer = document.querySelector(".h5-connection-container");
      const containerRect = connectionContainer?.getBoundingClientRect();

      if (!containerRect) continue;

      // 计算连接线的起始位置：
      // 从第一个步骤内容的顶部开始，加上圆圈的偏移量(25px + 19px圆圈半径 = 44px)
      const firstStepOffset = 44; // 圆圈中心位置

      // 计算两个步骤内容之间的实际距离
      const contentGap = nextContentRect.top - currentContentRect.top;

      // 连接线起始位置：相对于连接线容器
      const startY = currentContentRect.top - containerRect.top + firstStepOffset;
      // 连接线高度：两个步骤之间的距离
      const height = contentGap;

      lines.push({ startY, height });
    }

    h5ConnectionLines.value = lines;
  });
};

// H5版本的滚动监听逻辑 - 分段连接线版本
const handleH5Scroll = () => {
  if (!h5ContentRefs.value.length) return;

  const windowHeight = window.innerHeight;
  let activeStep = 0;
  for (let i = 0; i < h5ContentRefs.value.length; i++) {
    const element = h5ContentRefs.value[i];
    if (!element) continue;

    const rect = element.getBoundingClientRect();
    const triggerPoint = (windowHeight * 3) / 4;

    if (rect.top <= triggerPoint) {
      activeStep = i;
    }
  }

  h5CurrentStep.value = activeStep;
};

onMounted(() => {
  handleServiceHover(consolidationInclusions[0], 0);

  calculateH5ConnectionLines();
  window.addEventListener("scroll", handleH5Scroll, { passive: true });
  window.addEventListener("resize", calculateH5ConnectionLines, {
    passive: true,
  });
  handleH5Scroll();
});

onUnmounted(() => {
  window.removeEventListener("scroll", handleH5Scroll);
  window.removeEventListener("resize", calculateH5ConnectionLines);
});

const handleServiceHover = (item: any, _index: number) => {
  const title = item.title;
  const currentIndex = pageData.activeInclusions.indexOf(title);

  if (currentIndex === -1) {
    // 未展开则添加
    pageData.activeInclusions.push(title);
  } else {
    // 已展开则移除
    pageData.activeInclusions.splice(currentIndex, 1);
  }
};
</script>
<style scoped lang="scss">
.page-header {
  width: 100%;
  height: 11.6rem;
  position: relative;
  background: rgba(0, 0, 0, 0.45);
}
</style>
