<template>
  <div class="w-full bg-white">
    <div class="page-header text-[#fff] overflow-auto px-[0.48rem] py-[1rem]">
      <Breadcrumb :items="breadcrumbItems" />
      <div class="text-[1.04rem] font-500 leading-[1.14rem] mt-[0.8rem]">Contacto</div>
    </div>
    <div class="pt-[0.8rem]">
      <div class="text-center text-[0.56rem] leading-[0.68rem] font-400 text-[#333]">Contacto</div>
      <div class="text-center text-[0.32rem] leading-[0.48rem] font-400 text-[#7F7F7F] pt-[0.36rem]">
        <div class="flex flex-wrap px-[0.4rem]">
          Un representante de Chilat estará en contacto dentro de las 48 horas para organizar una consulta inicial y
          guiarle a través del proceso.
        </div>
      </div>
      <div class="flex justify-center">
        <hr class="w-[1rem] mt-[0.4rem] border-t-2 border-[#e50113]" />
      </div>
    </div>
    <!-- 表单 -->
    <div class="rounded-[0.4rem] border border-[#E6E6E6] mt-[0.8rem] px-[0.2rem] pb-[0.16rem] mx-[0.16rem]">
      <n-form ref="editFormRef" :model="editForm" :rules="editRules">
        <n-form-item
          path="contactName"
          label="Nombre"
          label-style="color: #333; font-weight: 500; font-size: 0.28rem"
          class="pt-[0.36rem]"
        >
          <n-input
            class="custom-input"
            v-model:value="editForm.contactName"
            @keydown.enter.prevent
            placeholder="Por favor, introduzca"
            @blur="onBlurEvent(editForm.contactName, 'Nombre')"
          />
        </n-form-item>
        <hr class="border-t border-[#F2F2F2] mb-[0.32rem]" />
        <n-form-item
          path="countryId"
          label="País"
          label-style="color: #333; font-weight: 500; font-size: 0.28rem"
          class="pt-[0.36rem]"
        >
          <n-select
            filterable
            class="custom-input"
            value-field="id"
            label-field="countryEsName"
            v-model:value="editForm.countryId"
            :options="pageData.countryList"
            placeholder="Por favor, elija"
            @update:value="(value, option) => onSelectCountry(value, option)"
          />
        </n-form-item>
        <hr class="border-t border-[#F2F2F2] mb-[0.32rem]" />
        <n-form-item
          path="whatsapp"
          label="Whatsapp"
          label-style="color: #333; font-weight: 500; font-size: 0.28rem"
          class="pt-[0.36rem]"
        >
          <div class="!w-6">
            <span v-if="pageData?.countryRegexps?.areaCode">{{ pageData.countryRegexps.areaCode }}</span>
            <span class="text-[#A6A6A6]" v-else>+000</span>
          </div>
          <n-divider vertical class="h-full" />
          <n-input
            v-trim
            clearable
            maxlength="64"
            class="custom-input"
            @keydown.enter.prevent
            v-model:value="editForm.whatsapp"
            placeholder="Por favor, introduzca"
            @blur="onBlurEvent(editForm.whatsapp, 'Whatsapp')"
          />
        </n-form-item>
        <hr class="border-t border-[#F2F2F2] mb-[0.32rem]" />
        <n-form-item
          path="email"
          label="Correo electrónico"
          label-style="color: #333; font-weight: 500; font-size: 0.28rem"
          class="pt-[0.36rem]"
        >
          <n-input
            v-trim
            clearable
            v-model:value="editForm.email"
            placeholder="Por favor, introduzca"
            class="custom-input"
            @keydown.enter.prevent
            @blur="onBlurEvent(editForm.email, 'Correo electrónico')"
          />
        </n-form-item>
        <hr class="border-t border-[#F2F2F2] mb-[0.32rem]" />
        <n-form-item
          label="¿Quién eres?"
          path="userType"
          label-style="color: #333; font-weight: 500; font-size: 0.28rem"
          class="pt-[0.36rem]"
        >
          <n-radio-group
            v-model:value="editForm.userType"
            :on-update:value="(value) => onSelectEvent(value, 'userType', '¿Para qué está importando?')"
          >
            <n-flex vertical>
              <n-radio v-for="item in userTypeList" :value="item.value" :key="item.value" class="mt-[0.08rem]">
                <span>{{ item.label }}</span>
              </n-radio>
            </n-flex>
            <div
              path="userTypeRemark"
              class="mt-[0.12rem] w-[6.5rem]"
              v-if="editForm.userType === 'POTENTIAL_USER_TYPE_OTHER'"
            >
              <n-input
                round
                v-trim
                clearable
                maxlength="200"
                @keydown.enter.prevent
                v-model:value="editForm.userTypeRemark"
                placeholder="Por favor, introduzca"
              />
            </div>
          </n-radio-group>
        </n-form-item>
        <hr class="border-t border-[#F2F2F2] mb-[0.32rem]" />
        <n-form-item
          label="¿Tiene experiencia en importación?"
          path="withImportExperience"
          label-style="color: #333; font-weight: 500; font-size: 0.28rem"
          class="pt-[0.36rem]"
        >
          <n-radio-group
            v-model:value="editForm.withImportExperience"
            :on-update:value="
              (value) => onSelectEvent(value, 'withImportExperience', '¿Tiene experiencia en importación?')
            "
          >
            <n-space>
              <n-radio v-for="item in withImportExperienceList" :value="item.value" :key="item.value">{{
                item.label
              }}</n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item>
        <hr class="border-t border-[#F2F2F2] mb-[0.32rem]" />
        <n-form-item
          path="purchaseQuantity"
          label="¿Cantidad?"
          label-style="color: #333; font-weight: 500; font-size: 0.28rem"
          class="pt-[0.36rem]"
        >
          <n-radio-group
            v-model:value="editForm.purchaseQuantity"
            :on-update:value="(value) => onSelectEvent(value, 'purchaseQuantity', '¿Cantidad?')"
          >
            <n-flex vertical>
              <n-radio v-for="item in purchaseQuantityList" :value="item.value" :key="item.value">{{
                item.label
              }}</n-radio>
            </n-flex>
          </n-radio-group>
        </n-form-item>
        <hr class="border-t border-[#F2F2F2] mb-[0.32rem]" />
        <n-form-item
          path="buyerRemark"
          label="Consulta"
          label-style="color: #333; font-weight: 500; font-size: 0.28rem"
          class="pt-[0.36rem]"
        >
          <n-input
            class="rounded-[0.4rem]"
            type="textarea"
            show-count
            :autosize="{
              minRows: 3,
              maxRows: 5,
            }"
            v-model:value="editForm.buyerRemark"
          >
          </n-input>
        </n-form-item>
      </n-form>
      <div class="flex mt-[0.68rem] gap-[0.4rem]">
        <div class="flex-1">
          <n-flex justify="center" vertical>
            <div class="flex justify-center">
              <img
                src="@/assets/icons/common/wallet.svg"
                alt="contacto"
                class="w-[0.64rem] mr-[0.2rem]"
                loading="lazy"
              />
            </div>
            <div
              class="flex justify-center flex-wrap text-[0.28rem] leading-[0.34rem] font-400 text-[#333] leading-[0.44rem]"
            >
              Se recomienda que monto mínima sea de US$ 10,000.
            </div>
          </n-flex>
        </div>
        <div class="flex-1">
          <n-flex justify="center" vertical>
            <div class="flex justify-center">
              <img
                src="@/assets/icons/common/inquire.svg"
                alt="contacto"
                class="w-[0.64rem] mr-[0.2rem]"
                loading="lazy"
              />
            </div>
            <div
              class="flex justify-center flex-wrap text-[0.28rem] leading-[0.34rem] font-400 text-[#333] leading-[0.44rem]"
            >
              No aceptamos consultas de productos infractores, gracias.
            </div>
          </n-flex>
        </div>
      </div>
      <div class="mt-[0.68rem]">
        <n-button
          color="#E50113"
          @click="onPotentialUserSubmit"
          :loading="pageData.submitLoading"
          data-spm-box="potential_user_note_submit"
          class="w-full px-8 py-[0.44rem] rounded-[0.4rem] text-[0.36rem] leading-[0.36rem] font-bold"
        >
          <div class="flex items-center justify-center">Enviar</div>
        </n-button>
      </div>
    </div>
    <div class="mt-[1.6rem] px-[0.4rem]">
      <!-- 中国办事处 -->
      <div>
        <div class="flex items-start">
          <img src="@/assets/icons/common/address.svg" alt="contacto" class="w-[0.68rem] mr-[0.2rem]" loading="lazy" />
          <div class="pt-[0.12rem]">
            <div class="text-[0.4rem] leading-[0.4rem] font-400 text-[#333]">Oficina en China:</div>
          </div>
        </div>
        <div class="flex flex-wrap ml-[0.84rem] text-[#7F7F7F] text-[0.32rem] leading-[0.4rem] font-400">
          Room 2001, Building A, Liandu Mansion, Financial Business District, Yiwu, Zhejiang 322000, China
        </div>
        <div class="flex flex-wrap ml-[0.84rem] text-[#7F7F7F] text-[0.32rem] leading-[0.4rem] font-400 mt-[0.12rem]">
          707#, Jinke Building, No. 19-2 Guangwei Road, Yuexiu District, Guangzhou, China
        </div>
      </div>
      <!-- 邮箱 -->
      <div @click="onOpenMailClient" class="email-contact hover:cursor-pointer py-[0.2rem] mt-[0.2rem] h-[2rem]">
        <div class="flex items-start">
          <img src="@/assets/icons/common/email.svg" alt="contacto" class="w-[0.68rem] mr-[0.2rem]" loading="lazy" />
          <div>
            <div class="text-[0.4rem] leading-[6.4rem0.08rem] font-400">Email:</div>
          </div>
        </div>
        <div class="ml-[0.84rem] text-[#7F7F7F] text-[0.28rem] leading-[0.44rem]">
          <div><EMAIL></div>
        </div>
      </div>
      <!-- WhatsApp -->
      <div @click="onWhatsAppClick()" class="whatsapp-contact hover:cursor-pointer py-[0.2rem] mt-[0.2rem] h-[2rem]">
        <div class="flex items-start">
          <img src="@/assets/icons/common/whatsapp.svg" alt="contacto" class="w-[0.68rem] mr-[0.2rem]" loading="lazy" />
          <div class="pt-[0.12rem]">
            <div class="text-[0.4rem] leading-[0.4rem] text-[#333] font-400">WhatsApp:</div>
          </div>
        </div>
        <div class="ml-[0.84rem] text-[#7F7F7F] text-[0.28rem] leading-[0.44rem]">
          <div>Haz clic aquí para chatear con</div>
          <div>nosotros.</div>
        </div>
      </div>
      <div class="mt-[1.6rem]"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import homeIcon from "@/assets/icons/common/home.svg";
import type { FormInst, FormItemRule, FormRules } from "naive-ui";

useHead({
  title: "Contacto - Chilat",
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/contacto/`,
    },
  ],
});

const config = useRuntimeConfig();
const editFormRef = ref<FormInst | null>(null);

const pageData = reactive<any>({
  submitLoading: false,
});
const editForm = reactive<any>({
  countryId: null,
  contactName: null,
  whatsapp: null,
  email: null,
  userType: null,
  userTypeRemark: null,
  withImportExperience: null,
  purchaseQuantity: null,
  buyerRemark: null,
});

const breadcrumbItems: any = [
  { link: "/h5", icon: homeIcon, alt: "home" },
  { link: "/h5/contacto", text: "Contacto" },
];

const editRules: FormRules = {
  contactName: {
    required: true,
    trigger: "blur",
    message: "Por favor, introduzca",
    validator(_rule: FormItemRule, value: any) {
      return !!value?.trim();
    },
  },
  countryId: {
    required: true,
    trigger: "blur",
    message: "Por favor, elija",
    validator(_rule: FormItemRule, value: any) {
      return !!value?.trim();
    },
  },
  whatsapp: {
    required: true,
    trigger: "blur",
    message: "Por favor, introduzca",
    validator(rule: FormItemRule, value: any) {
      const lengths =
        pageData?.countryRegexps?.phoneCount && pageData?.countryRegexps?.phoneCount.split(",").map(Number);
      if (value && value.length && lengths && lengths.length > 0) {
        for (const length of lengths) {
          // 如果匹配任何一个长度，返回 true
          if (value.length === length) {
            return true;
          }
        }
      } else {
        if (value) {
          return true;
        }
      }
      return false;
    },
  },
  email: {
    required: true,
    trigger: "blur",
    message: "Introduzca el buzón correcto",
    validator(rule: FormItemRule, value: any, callback: Function) {
      const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      // 未填写邮箱
      if (!value) {
        return callback(new Error("Por favor, introduzca"));
      }
      // 格式不正确
      if (!pattern.test(value)) {
        return callback(new Error("Introduzca el buzón correcto"));
      }
      return callback();
    },
  },
  userType: {
    required: true,
    trigger: "blur",
    message: "Por favor, elija",
    validator(_rule: FormItemRule, value: any) {
      return !!value?.trim();
    },
  },
  withImportExperience: {
    required: true,
    trigger: "blur",
    message: "Por favor, elija",
    validator(_rule: FormItemRule, value: any) {
      return !!value?.trim();
    },
  },
  purchaseQuantity: {
    required: true,
    trigger: "blur",
    message: "Por favor, elija",
    validator(_rule: FormItemRule, value: any) {
      return !!value?.trim();
    },
  },
};

await onGetCountry();

const userTypeList = [
  {
    value: "POTENTIAL_USER_TYPE_WHOLESALE",
    label: "Mayorista",
  },
  {
    value: "POTENTIAL_USER_TYPE_RETAIL",
    label: "Minoristas",
  },
  {
    value: "POTENTIAL_USER_TYPE_ONLINE_SHOP",
    label: "E-Commerce",
  },
  {
    value: "POTENTIAL_USER_TYPE_OTHER",
    label: "Otros",
  },
];

const purchaseQuantityList = [
  {
    value: "POTENTIAL_PURCHASE_QUANTITY_LCL",
    label: "Suelto",
  },
  {
    value: "POTENTIAL_PURCHASE_QUANTITY_FCL",
    label: "Por Contenedor",
  },
  {
    value: "POTENTIAL_PURCHASE_QUANTITY_NOT_SURE",
    label: "No sabe",
  },
];

const withImportExperienceList = [
  {
    value: "true",
    label: "Sí",
  },
  {
    value: "false",
    label: "No",
  },
];

// 输入框埋点事件
async function onBlurEvent(value: string, label: any, form?: any) {
  window?.MyStat?.addPageEvent("potential_user_input", `${label} 输入：${value}`);
}

function onSelectCountry(value: any, country: any) {
  window?.MyStat?.addPageEvent(
    "potential_user_select",
    `${country.countryEsName} 选择：${country?.countryName} （原值：${pageData?.countryRegexps?.countryName || "无"}）`,
  );
  pageData.countryRegexps = country;
  // 如果有长度校验 则校验长度
  if (pageData.countryRegexps.phoneCount) {
    editRules[
      "whatsapp"
    ].message = `El número de teléfono debe ser un número de ${pageData.countryRegexps.phoneCount} dígitos y sin ningún otro carácter.`;
  } else {
    // 没有长度校验 校验必填
    editRules["whatsapp"].message = `Por favor, introduzca WhatsApp`;
  }
}

// 下拉选择埋点事件
function onSelectEvent(value: string, attr: any, label: any) {
  editForm[attr] = value;
  let list = <any>[];
  if (attr === "userType") {
    list = userTypeList;
  }

  if (attr === "withImportExperience") {
    list = withImportExperienceList;
  }

  if (attr === "purchaseQuantity") {
    list = purchaseQuantityList;
  }

  const match = list.find((item: any) => item.value === value);
  if (!value) return;
  window?.MyStat?.addPageEvent("potential_user_select", `${label} 选择：${match?.label}`);
}

async function onGetCountry() {
  const res: any = await useGetCountry({});
  if (res?.result?.code === 200) {
    pageData.countryList = res?.data;
    if (config.public.defaultCountryCode) {
      res?.data.map((country: any) => {
        if (country.countryCodeTwo === config.public.defaultCountryCode) {
          editForm.countryId = country.id;
          pageData.countryRegexps = country;
          if (pageData.countryRegexps.phoneCount) {
            editRules[
              "whatsapp"
            ].message = `El número de teléfono debe ser un número de ${pageData.countryRegexps.phoneCount} dígitos y sin ningún otro carácter.`;
          }
        }
      });
    }
  }
}

async function onPotentialUserSubmit() {
  try {
    const isValid = await editFormRef.value?.validate();
    if (isValid) {
      pageData.submitLoading = true;
      const res: any = await useSaveUserInfo(editForm);
      pageData.submitLoading = false;
      if (res?.result?.code === 200) {
        window?.MyStat?.addPageEvent("potential_user_submit_success", `保存潜客信息成功，顺序号：${res?.data?.seqNo}`);
        showToast("Mandar exitosa");
        setTimeout(() => {
          navigateTo(`/`);
        }, 1500);
      } else {
        showToast(res?.result?.message);
        window?.MyStat?.addPageEvent("potential_user_submit_error", `潜客信息表单错误：${res?.result?.message}`);
      }
    }
  } catch (error) {
    pageData.submitLoading = false;
  }
}

function onOpenMailClient() {
  // 手动打开 mailto 链接
  window.location.href = "mailto:<EMAIL>";
}
</script>

<style scoped lang="scss">
.page-header {
  width: 100%;
  height: 8rem;
  position: relative;
  object-fit: cover;
  background-size: 100%100%;
  background-image: url("@/assets/icons/contacto/mobile-header-bg.png");
  background-repeat: no-repeat;
}

.whatsapp-contact {
  &:hover {
    color: #25d366;

    img {
      content: url("@/assets/icons/common/whatsapp-highlight.svg");
    }

    div,
    p {
      color: #25d366;
    }
  }
}

.email-contact {
  &:hover {
    color: #e50113;

    img {
      content: url("@/assets/icons/common/email-highlight.svg");
    }

    div,
    p {
      color: #e50113;
    }
  }
}

.custom-input {
  border: none !important;
  --n-border: none !important;
  --n-border-warning: none;
  --n-border-focus-warning: none;
  --n-border-hover-warning: none;
  --n-border: none;
  --n-border-disabled: none;
  --n-border-hover: none;
  --n-border-focus: none;
  --n-box-shadow-focus: none;
  --n-box-shadow-focus-warning: none;
  --n-border-error: none;
  --n-border-focus-error: none;
  --n-border-hover-error: none;
  --n-box-shadow-focus-error: none;
  --n-border-active-error: none;
  --n-box-shadow-active: none;
  --n-border-active: none;
  :deep(.n-base-selection) {
    border: none !important;
    --n-border: none !important;
    --n-border-warning: none;
    --n-border-focus-warning: none;
    --n-border-hover-warning: none;
    --n-border: none;
    --n-border-disabled: none;
    --n-border-hover: none;
    --n-border-focus: none;
    --n-box-shadow-focus: none;
    --n-box-shadow-focus-warning: none;
    --n-border-error: none;
    --n-border-focus-error: none;
    --n-border-hover-error: none;
    --n-box-shadow-focus-error: none;
    --n-border-active-error: none;
    --n-box-shadow-active: none;
    --n-border-active: none;
  }
}

.left-form-item {
  padding-top: 0.36rem;
  padding-bottom: 0.36rem;
  width: 100%;
  background: #fff;
  position: relative;
  border-bottom: 0.02rem solid #e6e6e6;
  overflow: visible;
  :deep(.n-form-item-feedback-wrapper) {
    position: absolute;
    bottom: -0.72rem;
    left: -2.8rem;
    color: #e50113;
    z-index: 2;
  }
}

.top-form-item {
  padding-top: 0.6rem;
  padding-bottom: 0.2rem;
  position: relative;
  border-bottom: 0.02rem solid #e6e6e6;
  :deep(.n-form-item-feedback-wrapper) {
    position: absolute;
    bottom: -0.72rem;
    left: 0;
    color: #e50113;
    z-index: 2;
  }
}
</style>

// src\assets\icons\contacto\mobile-header-bg.png
