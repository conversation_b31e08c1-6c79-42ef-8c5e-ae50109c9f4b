<template>
  <div class="w-full bg-white">
    <div class="page-header px-[0.4rem] text-[#fff] overflow-auto pt-[0.6rem]">
      <Breadcrumb :items="breadcrumbItems" />
      <div class="text-[1.04rem] leading-[1.16rem] font-medium mt-[0.6rem]">Quiénes somos</div>
    </div>
    <div class="px-[0.4rem] pt-[0.8rem] pb-[1.6rem]">
      <div class="text-[0.56rem] leading-[0.68rem]">¿Qué es Chilat?</div>
      <div class="text-[0.32rem] leading-[0.48rem] mt-[0.36rem] text-[#7F7F7F]">
        Somos el agente de compra y abastecimiento más grande de China dedicado a servir a importadores de habla
        hispana.<br />
        Brindamos a nuestros clientes un conjunto de soluciones para todas las importaciones desde China.
      </div>
      <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mt-[0.6rem]"></div>
      <div class="mt-[0.96rem] flex flex-col gap-[0.68rem]">
        <div v-for="(info, index) in chilatInfo" :key="index" class="flex items-center">
          <img loading="lazy" :src="info.icon" alt="quienes somos" class="w-[0.76rem] mr-[0.4rem]" />
          <div class="text-[0.36rem] leading-[0.44rem]">
            {{ info.title }}
          </div>
        </div>
      </div>
    </div>
    <div class="pt-[0.8rem] bg-[#FAFAFA] text-center">
      <div class="text-[0.56rem] leading-[0.68rem]">Nuestros servicio</div>
      <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mt-[0.36rem] mx-auto"></div>
      <n-grid :cols="2" class="mt-[0.8rem] w-full px-0">
        <n-grid-item
          v-for="(service, index) in serviceInfo"
          :key="index"
          :class="[
            'hover:bg-[#F2F2F2] transition-all duration-300 cursor-pointer border-solid border-[#F2F2F2]',
            {
              'border-t border-l': true,
              'border-r': index % 2 === 1 || (index === serviceInfo.length - 1 && serviceInfo.length % 2 === 1),
              'border-b': index >= serviceInfo.length - (serviceInfo.length % 2 === 0 ? 2 : 1),
            },
          ]"
          @mouseover="pageData.serviceIndex = index"
          @mouseleave="pageData.serviceIndex = -1"
        >
          <a class="block flex flex-col items-center px-[0.2rem] py-[0.52rem]" :href="service.path">
            <img
              loading="lazy"
              class="w-[0.96rem]"
              alt="quienes somos"
              :src="pageData.serviceIndex === index ? service.activatedIcon : service.icon"
            />
            <div class="text-[0.32rem] leading-[0.4rem] mt-[0.28rem]">
              {{ service.title }}
            </div>
          </a>
        </n-grid-item>
      </n-grid>
    </div>
    <div class="pt-[1.6rem] px-[0.4rem]">
      <div class="text-[0.56rem] leading-[0.68rem] text-center">¿Qué podemos hacer por usted?</div>
      <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mt-[0.36rem] mx-auto"></div>
      <div class="mt-[0.6rem]">
        <div class="py-[0.52rem] border-b-1 border-[#F2F2F2] flex" v-for="(support, index) in supportInfo" :key="index">
          <div class="text-[0.32rem] leading-[0.32rem] text-[#7F7F7F] mr-[0.28rem] mt-[0.06rem]">0{{ index + 1 }}</div>
          <div class="text-[0.36rem] leading-[0.44rem]">{{ support }}</div>
        </div>
      </div>
      <div class="flex justify-center mt-[1.2rem]">
        <n-button
          size="large"
          color="#fff"
          text-color="#e50113"
          @click="onWhatsAppClick"
          class="global-navigation-btn w-full rounded-[10rem] whitespace-normal h-[fit-content] px-[0.72rem]"
        >
          <div class="text-[0.32rem] leading-[0.4rem] py-[0.24rem]">
            ¡Necesito un agente que me de soporte ahora mismo!
          </div>
        </n-button>
      </div>
    </div>
    <div class="pt-[2rem] px-[0.4rem]">
      <div class="text-[0.68rem] leading-[0.68rem]">Nuestra ventaja</div>
      <div class="text-[0.32rem] leading-[0.48rem] text-[#7F7F7F] mt-[0.36rem]">
        A diferencia de otras empresas multilingües, como empresa de 22 años enfocada en el comercio exterior español,
        tenemos muchas ventajas únicas. Por favor, mire el video a continuación para más detalles.
      </div>
      <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mt-[0.6rem]"></div>
      <div class="w-[6.7rem] h-[3.7688rem] rounded-[0.4rem] overflow-hidden mt-[0.8rem]">
        <video-you-tube
          :width="335"
          :height="188.44"
          youtubeId="7c9B2YtloiM"
          titleCh="您企业的最佳伙伴#购买中国#义乌"
          poster="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/03/14/6ba06646-8108-40a6-8f8c-d6f994b649ea.png"
        ></video-you-tube>
      </div>

      <div class="flex items-center">
        <n-button
          size="large"
          color="#fff"
          text-color="#e50113"
          @click="onWhatsAppClick"
          class="global-navigation-btn h-[0.96rem] rounded-[10rem] mt-[1.08rem] mx-auto px-[0.84rem]"
        >
          <span class="text-[0.32rem] leading-[0.4rem]"> ¡Consultar ahora! </span>
        </n-button>
      </div>
    </div>
    <div class="pt-[2rem] pl-[0.4rem]">
      <div class="text-[0.56rem] leading-[0.68rem]">Nuestra oficina</div>
      <div class="text-[0.32rem] leading-[0.48rem] text-[#7F7F7F] mt-[0.36rem]">
        A medida que la compañía continúa creciendo, hemos experimentado cuatro generaciones de oficinas.
      </div>
      <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mt-[0.6rem]"></div>
      <div class="mt-[0.8rem] relative">
        <div class="w-full relative">
          <!-- 箭头 - 放在外层容器中，使其不随内容滚动 -->
          <div class="arrows-container absolute bottom-[-9.48rem] flex justify-between items-center pr-[0.4rem]">
            <img
              loading="lazy"
              alt="quienes somos"
              class="w-[0.96rem] left-arrow rotate-180 cursor-pointer transition-opacity duration-300 z-20"
              :class="{ 'opacity-50 cursor-not-allowed': pageData.isLeftEnd }"
              :src="pageData.isLeftEnd ? arrowFillGrey : arrowFillRed"
              @click="!pageData.isLeftEnd && scrollTimeline('left')"
            />
            <!-- 添加进度条 -->
            <div class="progress-bar-container h-[0.06rem] bg-[#E6E6E6] z-20 rounded-[0.04rem] flex-1 mx-[0.46rem]">
              <div
                class="progress-bar h-full bg-[#e50113] transition-all duration-300"
                :style="{ width: pageData.scrollProgress + '%' }"
              ></div>
            </div>
            <img
              loading="lazy"
              alt="quienes somos"
              class="w-[0.96rem] right-arrow cursor-pointer transition-opacity duration-300 z-20"
              :class="{ 'cursor-not-allowed': pageData.isRightEnd }"
              :src="pageData.isRightEnd ? arrowFillGrey : arrowFillRed"
              @click="!pageData.isRightEnd && scrollTimeline('right')"
            />
          </div>
          <!-- 时间线内容 - 放在可滚动容器中 -->
          <div class="relative h-[19.68rem] pt-[11.6rem] pb-[8.48rem] timeline-container">
            <div class="flex w-[20.56rem]">
              <!-- 时间线 -->
              <div class="h-[0.02rem] bg-[#ccc] w-[19.76rem]"></div>
              <!-- 占位 -->
              <div class="h-[0.02rem] w-[0.8rem]"></div>
            </div>
            <!-- 时间节点 -->
            <div class="flex relative">
              <!-- firstOffice -->
              <div class="absolute left-[1.4rem]">
                <!-- 时间点标记 -->
                <div class="w-[0.22rem] h-[0.22rem] bg-[#B3B3B3] rounded-full absolute top-[-0.1rem] z-11"></div>
                <div class="w-[0.02rem] h-[4.6rem] bg-[#CCC] absolute top-[-4.6rem] left-[0.1rem] z-10"></div>
                <!-- 时间图标 -->
                <img
                  loading="lazy"
                  alt="quienes somos"
                  :src="firstOfficeTime"
                  class="w-[6.04rem] absolute top-[-5.84rem] left-[-1.08rem] z-10 max-w-[initial]"
                />
                <!-- 办公室图片 -->
                <img
                  loading="lazy"
                  alt="quienes somos"
                  :src="firstOffice"
                  class="w-[6.7rem] absolute top-[-10.72rem] left-[-1.4rem] z-10 max-w-[initial]"
                />
                <!-- 标题 -->
                <div
                  class="text-[0.3rem] leading-[0.3rem] font-medium absolute top-[-11.28rem] left-[-1.4rem] z-10 whitespace-nowrap"
                >
                  Primera oficina
                </div>
              </div>
              <!--firstRenovation  -->
              <div class="absolute left-[4.32rem]">
                <!-- 时间点标记 -->
                <div class="w-[0.22rem] h-[0.22rem] bg-[#B3B3B3] rounded-full absolute top-[-0.1rem] z-11"></div>
                <div class="w-[0.02rem] h-[0.76rem] bg-[#CCC] absolute top-[-0.76rem] left-[0.1rem] z-10"></div>
                <!-- 时间图标 -->
                <img
                  loading="lazy"
                  alt="quienes somos"
                  :src="firstRenovationTime"
                  class="w-[1.16rem] absolute top-[-1.32rem] left-[-0.48rem] z-10 max-w-[initial]"
                />
                <!-- 办公室图片 -->
                <img
                  loading="lazy"
                  alt="quienes somos"
                  :src="firstRenovation"
                  class="w-[3.68rem] absolute top-[-4.04rem] left-[-1.72rem] z-10 max-w-[initial]"
                />
                <!-- 标题 -->
                <div
                  class="text-[0.3rem] leading-[0.3rem] font-medium absolute top-[-4.52rem] left-[-1.72rem] z-10 whitespace-nowrap"
                >
                  Renovación
                </div>
              </div>
              <!-- secondOffice -->
              <div class="absolute top-[0] left-[7.36rem]">
                <div class="w-[0.22rem] h-[0.22rem] bg-[#B3B3B3] rounded-full absolute top-[-0.1rem] left-0 z-11"></div>
                <div
                  class="w-[0.02rem] h-[0.8rem] bg-[#CCC] absolute top-[-0.1rem] left-[0.1rem] z-10 max-w-[initial]"
                ></div>
                <div
                  class="text-[0.3rem] leading-[0.3rem] font-medium absolute top-[0.88rem] left-[-1.72rem] whitespace-nowrap"
                >
                  La segunda oficina
                </div>
                <img
                  loading="lazy"
                  alt="quienes somos"
                  :src="secondOffice"
                  class="w-[3.66rem] absolute top-[1.36rem] left-[-1.72rem] max-w-[initial]"
                />
                <img
                  loading="lazy"
                  alt="quienes somos"
                  :src="secondOfficeTime"
                  class="w-[3.28rem] absolute top-[4.12rem] left-[-1.72rem] max-w-[initial]"
                />
              </div>
              <!-- thirdOffice -->
              <div class="absolute top-[0] left-[11.32rem]">
                <div class="w-[0.22rem] h-[0.22rem] bg-[#B3B3B3] rounded-full absolute top-[-0.1rem] left-0 z-11"></div>
                <div
                  class="w-[0.02rem] h-[0.8rem] bg-[#CCC] absolute top-[-0.1rem] left-[0.1rem] z-10 max-w-[initial]"
                ></div>
                <div
                  class="text-[0.3rem] leading-[0.3rem] font-medium absolute top-[0.88rem] left-[-1.72rem] whitespace-nowrap"
                >
                  Tercera oficina
                </div>

                <img
                  loading="lazy"
                  alt="quienes somos"
                  :src="thirdOffice"
                  class="w-[3.66rem] absolute top-[1.36rem] left-[-1.72rem] max-w-[initial]"
                />
                <img
                  loading="lazy"
                  alt="quienes somos"
                  :src="thirdOfficeTime"
                  class="w-[3.28rem] absolute top-[4.12rem] left-[-1.72rem] max-w-[initial]"
                />
              </div>
              <!-- fourthOffice -->
              <div class="absolute top-[0] left-[16.52rem]">
                <div class="custom-icon absolute top-[-0.18rem] left-0 z-11">
                  <span></span>
                </div>
                <div
                  class="w-[0.02rem] h-[0.8rem] bg-[#e50113] absolute top-[-0.1rem] left-[0.16rem] z-10 max-w-[initial]"
                ></div>
                <div
                  class="text-[0.3rem] leading-[0.3rem] font-medium absolute top-[0.88rem] left-[-2.96rem] whitespace-nowrap"
                >
                  Cuarta oficina
                </div>

                <img
                  loading="lazy"
                  alt="quienes somos"
                  :src="fourthOffice"
                  class="w-[6.2rem] absolute top-[1.36rem] left-[-2.96rem] max-w-[initial]"
                />
                <img
                  loading="lazy"
                  alt="quienes somos"
                  :src="fourthOfficeTime"
                  class="w-[4rem] absolute top-[5.84rem] left-[-2.96rem] max-w-[initial]"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="pt-[2rem] px-[0.4rem]">
      <div class="text-[0.56rem] leading-[0.68rem]">Nuestro equipo</div>
      <div class="text-[0.32rem] leading-[0.48rem] text-[#7f7f7f] mt-[0.36rem]">
        <div>
          Ahora tenemos un equipo de 80 personas en Yiwu y hay 6 personas en la oficina de Guangzhou, incluyendo 40
          personas de servicio al cliente de hispanohablante.
        </div>
        <div>
          Ahora se ha convertido en la empresa profesional más grande de Yiwu al servicio de los países de habla
          hispana.
          <br />
          Los siguientes son nuestros Staff Administrativos y sus datos personales:
        </div>
      </div>
      <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mt-[0.6rem]"></div>
      <div class="text-[0.48rem] leading-[0.48rem] mt-[1.2rem]">Dirección Ejecutiva</div>
      <n-space vertical class="mt-[0.8rem]" :style="{ gap: '0.4rem 0' }">
        <div v-for="(manager, index) in managementInfo" :key="index" class="w-full">
          <div class="relative">
            <div @click="pageData.activeManager = index">
              <img loading="lazy" :src="manager.image" alt="quienes somos" class="w-full rounded-[0.4rem]" />
            </div>
            <div
              class="w-full h-full absolute top-0 left-0 text-white py-[0.36rem] pl-[0.36rem] overflow-hidden text-[0.32rem] leading-[0.48rem] rounded-[0.4rem] transition-all duration-300 ease-in-out"
              style="background-color: rgba(0, 0, 0, 0.55)"
              :class="{
                'opacity-100 visible': pageData.activeManager === index,
                'opacity-0 invisible': pageData.activeManager !== index,
              }"
              @click="pageData.activeManager = -1"
            >
              <div style="width: 100%; text-align: initial" trigger="none" class="h-full overflow-auto pr-[0.36rem]">
                <div v-html="manager.description"></div>
              </div>
            </div>
          </div>
          <div class="text-[0.4rem] leading-[0.4rem] mt-[0.32rem] text-center">
            {{ manager.name }}
          </div>
          <div class="text-[0.32rem] leading-[0.48rem] mt-[0.2rem] text-[#7f7f7f] text-center px-[0.34rem]">
            {{ manager.position }}
          </div>
        </div>
      </n-space>
    </div>
    <div class="pt-[1.2rem] custom-cursor" ref="teamCarouselSection">
      <div class="text-[0.48rem] leading-[0.48rem] text-center">Fotos del equipo</div>
      <MobileCarousel
        ref="teamCarousel1"
        :images="imageArr1"
        direction="left"
        :speed="20"
        :autoplay="true"
        :autoPlayWhenVisible="true"
        :gap="0"
        imageWidth="auto"
        imageHeight="4rem"
        class="mt-[0.8rem]"
      />
      <MobileCarousel
        ref="teamCarousel2"
        :images="imageArr2"
        direction="right"
        :speed="20"
        :autoplay="true"
        :autoPlayWhenVisible="true"
        :gap="0"
        imageWidth="auto"
        imageHeight="4rem"
        class="mt-[0.16rem]"
      />
    </div>
    <div class="pt-[1.6rem] overflow-auto custom-cursor" ref="clientCarouselSection">
      <div class="text-[0.56rem] leading-[0.68rem] px-[0.4rem]">Nuestros clientes</div>
      <div class="text-[0.32rem] leading-[0.48rem] text-[#7f7f7f] mt-[0.36rem] px-[0.4rem]">
        Desde el año 2003, hemos atendido a más de 1.500 clientes. Se extienden por toda Latinoamérica y España. Su
        aprobación hacia nosotros es la mayor fuerza impulsora de nuestro crecimiento.
      </div>
      <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mt-[0.6rem] ml-[0.4rem]"></div>
      <MobileCarousel
        ref="clientCarousel1"
        :images="imageArr3"
        direction="left"
        :speed="20"
        :autoplay="true"
        :autoPlayWhenVisible="true"
        :gap="0"
        imageWidth="auto"
        imageHeight="4rem"
        class="mt-[0.8rem]"
      />
      <MobileCarousel
        ref="clientCarousel2"
        :images="imageArr4"
        direction="right"
        :speed="20"
        :autoplay="true"
        :autoPlayWhenVisible="true"
        :gap="0"
        imageWidth="auto"
        imageHeight="4rem"
        class="mt-[0.16rem]"
      />
      <MobileCarousel
        ref="clientCarousel3"
        :images="imageArr5"
        direction="left"
        :speed="20"
        :autoplay="true"
        :autoPlayWhenVisible="true"
        :gap="0"
        imageWidth="auto"
        imageHeight="4rem"
        class="mt-[0.16rem]"
      />
    </div>
    <div class="px-[0.4rem] pt-[2rem]" id="empleo">
      <div class="w-[6.7rem] h-[3.7688rem] rounded-[0.4rem] overflow-hidden">
        <video-you-tube
          :width="335"
          :height="188.44"
          youtubeId="zHrBAsWg3WM"
          titleCh="与中国的进口和业务"
          poster="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/03/17/99573a79-fe41-4cc3-8107-731a0fc1528c.png"
        ></video-you-tube>
      </div>
      <div class="text-[0.32rem] leading-[0.48rem] text-[#7f7f7f] mt-[0.48rem]">
        Somos una empresa internacional con una cartera de clientes en el negocio de importaciones que expande también
        sus propuestas de servicios a proyectos de Inversión en China, Sourcing y Oportunidades de Negocios entre China
        para Latinoamérica y todos los mercados de habla hispana en el mundo.
      </div>
    </div>
    <div
      class="mt-[2rem] rounded-[0.4rem] w-full h-[4.04rem] text-[#fff] pt-[0.48rem] text-center"
      :style="{
        backgroundImage: `url(${redBg})`,
        backgroundSize: '100% 100%',
      }"
    >
      <div class="text-[0.48rem] leading-[0.56rem] font-medium">Solicita nuestra aseoria</div>
      <div class="text-[0.32rem] leading-[0.4rem] mt-[0.32rem]">Tenemos los servicios y experiencia que busca</div>
      <n-button
        size="large"
        color="#fff"
        text-color="#e50113"
        @click="onWhatsAppClick"
        class="h-[1.28rem] px-[0.48rem] rounded-[10rem] mt-[0.56rem] global-contact-btn"
      >
        <div class="text-[0.36rem] leading-[0.36rem] mr-[0.16rem]">Contacto</div>
        <img
          loading="lazy"
          alt="quienes somos"
          class="arrow-icon w-[0.88rem]"
          src="@/assets/icons/quienes-somos/arrow-line.svg"
        />
      </n-button>
    </div>
    <div class="py-[2rem] px-[0.4rem]">
      <div class="text-[0.56rem] leading-[0.68rem]">Nuestra historia</div>
      <div class="text-[0.32rem] leading-[0.48rem] text-[#7F7F7F] mt-[0.36rem]">
        Nuestra compañía se originó en 1998, la Sra. Lily Dai comenzó a comprar productos en el mercado de Yiwu y los
        vendía en Argentina. En el 2003, estableció una oficina en Yiwu y comenzó a ayudar a otros importadores a
        comprar productos en China…
      </div>
      <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mt-[0.6rem]"></div>
      <div class="w-[6.7rem] h-[3.7688rem] rounded-[0.4rem] overflow-hidden mt-[0.8rem]">
        <video-you-tube
          :width="335"
          :height="188.44"
          youtubeId="pj-CU1JJ8fc"
          titleCh="我们的故事"
          poster="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/03/18/7ff8355b-159e-4893-9a5d-f43d44af8cce.png"
        ></video-you-tube>
      </div>
    </div>
    <div class="pt-[0.8rem] pb-[2.4rem] px-[0.4rem] bg-[#FAFAFA] relative">
      <div class="text-[0.56rem] leading-[0.68rem] text-center">Misión,Visión,Valores</div>
      <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mt-[0.36rem] mx-auto"></div>
      <div class="mt-[0.8rem]">
        <div>
          <div class="flex">
            <img loading="lazy" :src="arrowRightBold" alt="quienes somos" class="w-[0.16rem] mr-[0.24rem]" />
            <div class="text-[0.44rem] leading-[0.44rem]">01</div>
          </div>
          <div class="text-[0.36rem] leading-[0.44rem] mt-[0.4rem]">Nuestra misión</div>
          <div class="text-[0.28rem] leading-[0.34rem] text-[#7F7F7F] mt-[0.24rem]">
            Brindar Servicios Corporativos de Excelencia para proyectos de importación desde China en el idioma español
            y abrir oportunidades de negocios exitosos para distintos rubros y mercados internacionales en el sector
            B2B.
          </div>
        </div>
        <div class="mt-[0.6rem]">
          <div class="flex">
            <img loading="lazy" :src="arrowRightBold" alt="quienes somos" class="w-[0.16rem] mr-[0.24rem]" />
            <div class="text-[0.44rem] leading-[0.44rem]">02</div>
          </div>
          <div class="text-[0.36rem] leading-[0.44rem] mt-[0.4rem]">Nuestra visión</div>
          <div class="text-[0.28rem] leading-[0.34rem] text-[#7F7F7F] mt-[0.24rem]">
            Ser reconocida en Latinoamérica y todas las comunidades de habla hispana como la empresa internacional líder
            que genera las mejores oportunidades de negocios con China basándose en el respeto por el cliente, la
            transparencia y honestidad en todo acuerdo comercial, agilidad, innovación y profesionalismo en los
            servicios que ofrece y por su calidez humana y relaciones de confianza a largo plazo.
          </div>
        </div>
        <div class="mt-[0.6rem]">
          <div class="flex">
            <img loading="lazy" :src="arrowRightBold" alt="quienes somos" class="w-[0.16rem] mr-[0.24rem]" />
            <div class="text-[0.44rem] leading-[0.44rem]">03</div>
          </div>
          <div class="text-[0.36rem] leading-[0.44rem] mt-[0.4rem]">Valores corporativos</div>
          <div class="text-[0.28rem] leading-[0.34rem] text-[#7F7F7F] mt-[0.24rem]">
            <div>
              <span class="text-[#e50113]">•Transparencia:</span>
              Ofrecemos servicios basados en la confianza que se demuestran con los hechos, sistemas de información que
              verifican nuestros procesos de calidad, ética comercial que privilegia la honestidad en todas las
              operaciones y en el diálogo directo con los líderes de nuestra compañía.
            </div>
            <div class="mt-[0.4rem]">
              <span class="text-[#e50113]">•Respeto:</span>
              Valoramos los intereses de nuestros clientes y aliados para encontrar acuerdos estratégicos que permiten
              crear situaciones de beneficios mutuo, equilibrados y exitosospara todos los involucrados en cada una de
              nuestras operaciones comerciales.
            </div>
            <div class="mt-[0.4rem]">
              <span class="text-[#e50113]">•Visión:</span>
              Trabajamos cada día para superar las expectativas de nuestros clientes ofreciendo servicios innovadores y
              analizando las condiciones de mercados rentables futuros.
            </div>
            <div class="mt-[0.4rem]">
              <span class="text-[#e50113]">•Calidad:</span>
              Creemos que las posibilidades de éxitos son posible gracias al trabajo realizado y fundamentalmente por la
              calidad de servicios que prestamos donde la satisfacción de nuestros clientes es la mejor promoción que
              tenemos en los mercados.
            </div>
            <div class="mt-[0.4rem]">
              <span class="text-[#e50113]">•Vínculo:</span>
              Forjamos una empresa con servicios de calidad internacional y también respetamos las tradiciones que
              priorizan en la calidad y cuidado de las relaciones humanas como la base del éxito a través del tiempo.
            </div>
            <div class="mt-[0.4rem]">
              <span class="text-[#e50113]">•Confianza:</span>
              Construimos relaciones a largo plazo en todos los negocios y acciones que emprendemos. Así logramos
              inspirar a empleados, clientes, socios y todos las personas relacionadas con nuestras actividades en
              seguir depositando su confianza a través del tiempo.
            </div>
          </div>
        </div>
      </div>
      <n-button
        size="large"
        color="#fff"
        text-color="#e50113"
        @click="onWhatsAppClick"
        class="w-full h-[0.96rem] rounded-[10rem] mt-[1.2rem] global-navigation-btn"
      >
        <span class="text-[0.36rem] leading-[0.36rem] mr-[0.32rem] py-[0.24rem]">¡Necesito un agente ahora!</span>
      </n-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import homeIcon from "@/assets/icons/common/home.svg";
import veteran from "@/assets/icons/quienes-somos/veteran.svg";
import branches from "@/assets/icons/quienes-somos/branches.svg";
import multilingual from "@/assets/icons/quienes-somos/multilingual.svg";
import procurementRed from "@/assets/icons/quienes-somos/procurement-red.svg";
import procurementBlack from "@/assets/icons/quienes-somos/procurement-black.svg";
import supplierRed from "@/assets/icons/quienes-somos/supplier-red.svg";
import supplierBlack from "@/assets/icons/quienes-somos/supplier-black.svg";
import validationRed from "@/assets/icons/quienes-somos/validation-red.svg";
import validationBlack from "@/assets/icons/quienes-somos/validation-black.svg";
import translationRed from "@/assets/icons/quienes-somos/translation-red.svg";
import translationBlack from "@/assets/icons/quienes-somos/translation-black.svg";
import agentRed from "@/assets/icons/quienes-somos/agent-red.svg";
import agentBlack from "@/assets/icons/quienes-somos/agent-black.svg";
import inspectionRed from "@/assets/icons/quienes-somos/inspection-red.svg";
import inspectionBlack from "@/assets/icons/quienes-somos/inspection-black.svg";
import consolidationRed from "@/assets/icons/quienes-somos/consolidation-red.svg";
import consolidationBlack from "@/assets/icons/quienes-somos/consolidation-black.svg";
import designRed from "@/assets/icons/quienes-somos/design-red.svg";
import designBlack from "@/assets/icons/quienes-somos/design-black.svg";
import firstOffice from "@/assets/icons/quienes-somos/first-office.png";
import firstRenovation from "@/assets/icons/quienes-somos/first-renovation.png";
import secondOffice from "@/assets/icons/quienes-somos/second-office.png";
import thirdOffice from "@/assets/icons/quienes-somos/third-office.png";
import fourthOffice from "@/assets/icons/quienes-somos/fourth-office.png";
import firstOfficeTime from "@/assets/icons/quienes-somos/first-office-time.svg";
import firstRenovationTime from "@/assets/icons/quienes-somos/first-renovation-time.svg";
import secondOfficeTime from "@/assets/icons/quienes-somos/second-office-time.svg";
import thirdOfficeTime from "@/assets/icons/quienes-somos/third-office-time.svg";
import fourthOfficeTime from "@/assets/icons/quienes-somos/fourth-office-time.svg";
import manager1 from "@/assets/icons/quienes-somos/manager1.jpg";
import manager2 from "@/assets/icons/quienes-somos/manager2.jpg";
import manager3 from "@/assets/icons/quienes-somos/manager3.jpg";
import manager4 from "@/assets/icons/quienes-somos/manager4.jpg";
import manager5 from "@/assets/icons/quienes-somos/manager5.jpg";
import manager6 from "@/assets/icons/quienes-somos/manager6.jpg";
import manager7 from "@/assets/icons/quienes-somos/manager7.jpg";
import manager8 from "@/assets/icons/quienes-somos/manager8.jpg";
import team1 from "@/assets/icons/quienes-somos/team1.png";
import team2 from "@/assets/icons/quienes-somos/team2.png";
import team3 from "@/assets/icons/quienes-somos/team3.png";
import team4 from "@/assets/icons/quienes-somos/team4.png";
import team5 from "@/assets/icons/quienes-somos/team5.png";
import team6 from "@/assets/icons/quienes-somos/team6.png";
import team7 from "@/assets/icons/quienes-somos/team7.png";
import team8 from "@/assets/icons/quienes-somos/team8.png";
import team9 from "@/assets/icons/quienes-somos/team9.png";
import team10 from "@/assets/icons/quienes-somos/team10.png";
import team11 from "@/assets/icons/quienes-somos/team11.png";
import team12 from "@/assets/icons/quienes-somos/team12.png";
import customer1 from "@/assets/icons/quienes-somos/customer1.png";
import customer2 from "@/assets/icons/quienes-somos/customer2.png";
import customer3 from "@/assets/icons/quienes-somos/customer3.png";
import customer4 from "@/assets/icons/quienes-somos/customer4.png";
import customer5 from "@/assets/icons/quienes-somos/customer5.png";
import customer6 from "@/assets/icons/quienes-somos/customer6.png";
import customer7 from "@/assets/icons/quienes-somos/customer7.png";
import customer8 from "@/assets/icons/quienes-somos/customer8.png";
import customer9 from "@/assets/icons/quienes-somos/customer9.png";
import customer10 from "@/assets/icons/quienes-somos/customer10.png";
import customer11 from "@/assets/icons/quienes-somos/customer11.png";
import customer12 from "@/assets/icons/quienes-somos/customer12.png";
import customer13 from "@/assets/icons/quienes-somos/customer13.png";
import customer14 from "@/assets/icons/quienes-somos/customer14.png";
import customer15 from "@/assets/icons/quienes-somos/customer15.png";
import customer16 from "@/assets/icons/quienes-somos/customer16.png";
import customer17 from "@/assets/icons/quienes-somos/customer17.png";
import customer18 from "@/assets/icons/quienes-somos/customer18.png";
import customer19 from "@/assets/icons/quienes-somos/customer19.png";
import customer20 from "@/assets/icons/quienes-somos/customer20.png";
import customer21 from "@/assets/icons/quienes-somos/customer21.png";
import redBg from "@/assets/icons/quienes-somos/mobile-red-bg.png";
import arrowRightBold from "@/assets/icons/quienes-somos/arrow-right-bold.svg";
import arrowFillRed from "@/assets/icons/quienes-somos/arrow-fill-red.svg";
import arrowFillGrey from "@/assets/icons/quienes-somos/arrow-fill-grey.svg";

useHead({
  title: "Quienes somos_Introducción al negocio de la empresa-Chilat",
  meta: [
    {
      name: "description",
      content:
        "Somos el agente de compras y abastecimiento más grande de China dedicado a servir a importadores de habla hispana. Ofrecemos a nuestros clientes un conjunto de soluciones para todas las importaciones desde China.",
    },
  ],
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/quienes-somos/`,
    },
  ],
});

const breadcrumbItems = [
  { link: "/h5", icon: homeIcon, alt: "home" },
  { text: "Nosotros" },
  { link: "/h5/quienes-somos", text: "Quiénes somos" },
];

const chilatInfo = [
  {
    icon: veteran,
    title: "22 años de experiencia",
  },
  {
    icon: multilingual,
    title: "Equipo de más de 40 hispanoparlantes",
  },
  {
    icon: branches,
    title: "Oficinas en Yiwu y Guangzhou",
  },
];

const serviceInfo = [
  {
    icon: procurementRed,
    activatedIcon: procurementBlack,
    title: "Asesoría de compras",
    path: "/h5/servicios#asesoria-de-compras",
  },
  {
    icon: supplierRed,
    activatedIcon: supplierBlack,
    title: "Búsqueda de proveedores y productos",
    path: "/h5/servicios#busqueda-de-proveedores-y-productos",
  },
  {
    icon: validationRed,
    activatedIcon: validationBlack,
    title: "Validación de fabricantes",
    path: "/h5/servicios#validacion-de-fabricantes",
  },
  {
    icon: translationRed,
    activatedIcon: translationBlack,
    title: "Servicio de traducción al español y asistente de viaje de negocios",
    path: "/h5/servicios#viajes-de-negocios-a-china",
  },
  {
    icon: agentRed,
    activatedIcon: agentBlack,
    title: "Agente de compras para los mercados",
    path: "/h5/viajar-a-china",
  },
  {
    icon: inspectionRed,
    activatedIcon: inspectionBlack,
    title: "Control de calidad e Inspección de mercancías",
    path: "/h5/servicios#control-de-calidad",
  },
  {
    icon: consolidationRed,
    activatedIcon: consolidationBlack,
    title: "Consolidación de mercaderías y logística",
    path: "/h5/consolidacion-de-cargas-y-logistica",
  },
  {
    icon: designRed,
    activatedIcon: designBlack,
    title: "Diseños e imágenes de producto",
    path: "/h5/servicios#fotos-del-producto-y-catalogo",
  },
];

const supportInfo = [
  "Con más de 20000 recursos de proveedores, podemos ayudarle a comparar fábricas para obtener precios más competitivos.",
  "Cuando no se encuentre en China, podemos ayudarle a encontrar los productos que necesita y ofrecerle una cotización en 48 horas.",
  "Podemos ayudarle a consolidar productos de múltiples proveedores en un solo contenedor.",
  "Comunicamos con distintos proveedores en espanol, y tambien supervisamos su pedido.",
  "Podemos ayudarle en el mercado de Yiwu, Guangzhou y mercados especializados a sus alrededores, con compra directa en fábrica.",
];

const managementInfo = [
  {
    image: manager2,
    name: "Wang Min 汪敏 (Mindy)",
    position: "CEO y Presidente del Grupo Chilat, Gerente General en MingZhan Trade Import & Export Co.,LTD.",
    description:
      "<div>Nació en Shanghái (China) en 1980. Es licenciada por la Universidad de Macquarie (Sídney, Australia) y habla tres idiomas: mandarín, inglés y español.</div><div>Desde 2002 trabaja en el negocio familiar como asistente de comercio exterior. Su negocio familiar importaba desde Argentina desde 1990. Es la única hija de la familia. Por eso decidió trabajar en el negocio familiar. La empresa familiar era importadora y agente de compras.</div><div>En 2011, ella y su esposo fundaron la empresa Mingzhan Imp. & Exp.</div><div>En 2016, fundó Chilat como una marca de servicio a clientes latinoamericanos. La empresa se centra en el mercado latinoamericano. La marca Chilat —China Latinoamérica— está registrada.</div><div>Cuenta con 23 años de experiencia como consultora especialista en comercio exterior. Su empresa ha ayudado a más de 1000 importadores de Latinoamérica y, como era importadora, entiende mejor que nadie.</div>",
  },
  {
    image: manager1,
    name: "Renyi 任毅 (Andy)",
    position: "CEO y Presidente del Grupo Chilat, Director General MingZhan Trade Import & Export Co.,LTD.",
    description:
      "<div>Estudió Economía Internacional en Sídney (Australia), donde residió durante años. </div><div>Tras su experiencia internacional, regresó a China para trabajar con su esposa en un nuevo concepto de trading y ampliar su visión comercial a nuevos desarrollos en el país, que actualmente tiene un gran potencial. Le gusta la cocina internacional y viajar.</div>",
  },
  {
    image: manager3,
    name: "Irene",
    position: "Gerente de cliente",
    description:
      "<div>Se llama Irene, trabaja en Chilat desde hace 16 años y tiene mucha experiencia en comercio exterior.</div><div>Estudió español durante 3 años en la universidad y luego empezó a trabajar en esta empresa.</div><div>Es muy simpática y tiene mucha paciencia.</div>",
  },
  {
    image: manager4,
    name: "Elisa Wang",
    position: "Gerente de cliente exterior de Chilat",
    description:
      "<div>Se llama Elisa, trabaja en Chilat desde hace 16 años.</div><div>Ayuda a muchos clientes a importar de China y a ampliar su negocio gracias a su amplia experiencia en importación y exportación.</div><div>Es una profesional apasionada por su trabajo, responsable con los clientes y con una actitud de servicio eficiente y paciente.</div>",
  },
  {
    image: manager5,
    name: "Tania",
    position: "Gerente de cliente",
    description:
      "<div>Se llama Tania y ha trabajado con Chilat durante 8 años.</div><div>Ha ayudado a muchos clientes de más de 13 países latinoamericanos a importar desde China y ampliar sus negocios. Tiene mucha experiencia y conocimientos sobre la importación de cada país.</div><div>Trabaja de manera eficiente y con mucha pasión.</div>",
  },
  {
    image: manager6,
    name: "Iris",
    position: "Gerente de cliente",
    description:
      "<div>Se llama Iris y trabaja en Chilat desde hace 8 años.</div><div>Cuenta con muchos años de experiencia en importación y exportación, y ha ayudado a clientes de países latinoamericanos a importar productos de China.</div><div>Es una apasionada de su trabajo, responsable con sus clientes y muy eficiente y paciente.</div>",
  },
  {
    image: manager7,
    name: "Celia",
    position: "Gerente de cliente",
    description:
      "<div>Se llama Celia. Ha trabajado con Chilat durante 8 años.</div><div>Su pronunciación es muy similar a la de «seria», pero también refleja que ella muy serio y responsable a la hora de gestionar todos los pedidos.</div><div>Trata sinceramente a cada cliente, pero en realidad el personaje es muy cálido y alegre.</div><div>Espera conocer a muchos amigos en Latinoamérica.</div>",
  },
  {
    image: manager8,
    name: "Karmen",
    position: "Gerente de cliente",
    description:
      "<div>Se llama Karmen Ye, ha vivido en Ecuador con toda su familia durante 8 años, ha estudiado y trabajado.</div><div>Es capaz de entender las necesidades de los demás con rapidez.</div><div>Es una chica muy simpática a la que le gusta viajar y hacer amigos, y es responsable y paciente en el trabajo.</div>",
  },
];

const imageArr1 = [team1, team2, team3, team4, team5, team6];
const imageArr2 = [team7, team8, team9, team10, team11, team12];
const imageArr3 = [customer1, customer2, customer3, customer4];
const imageArr4 = [customer5, customer6, customer7, customer8, customer9, customer10, customer11, customer12];
const imageArr5 = [
  customer13,
  customer14,
  customer15,
  customer16,
  customer17,
  customer18,
  customer19,
  customer20,
  customer21,
];

const pageData = reactive({
  serviceIndex: -1,
  // 添加时间线滚动状态控制
  isLeftEnd: true, // 初始时在最左侧，左箭头禁用
  isRightEnd: false,
  // 添加进度条状态
  scrollProgress: 0,
  // 添加管理团队交互状态
  activeManager: -1,
});

// 轮播图引用
const teamCarousel1 = ref(null);
const teamCarousel2 = ref(null);
const clientCarousel1 = ref(null);
const clientCarousel2 = ref(null);
const clientCarousel3 = ref(null);

// 轮播图区域引用
const teamCarouselSection = ref(null);
const clientCarouselSection = ref(null);

// 创建IntersectionObserver实例
const createIntersectionObserver = (targetRef: any, startCallback: any, stopCallback?: any) => {
  if (!targetRef.value) return;

  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          // 元素进入视口，启动轮播
          startCallback();
        } else {
          // // 元素离开视口，停止轮播
          stopCallback();
        }
      });
    },
    { threshold: 0.2 },
  ); // 当20%的元素可见时触发

  observer.observe(targetRef.value);

  return observer;
};

// 时间线滚动方法
const scrollTimeline = (direction: "left" | "right") => {
  const container = document.querySelector(".timeline-container");
  if (!container) return;

  const scrollAmount = 400; // 每次滚动的距离
  const currentScroll = container.scrollLeft;
  const newScroll = direction === "left" ? currentScroll - scrollAmount : currentScroll + scrollAmount;

  // 使用平滑滚动效果
  container.scrollTo({
    left: newScroll,
    behavior: "smooth",
  });
};

// 监听滚动事件，更新箭头状态
onMounted(() => {
  const container = document.querySelector(".timeline-container");
  if (!container) return;

  // 初始检查
  checkScrollPosition(container);

  // 设置初始进度条值
  setTimeout(() => {
    checkScrollPosition(container);
  }, 300);

  // 监听滚动事件
  container.addEventListener("scroll", () => {
    checkScrollPosition(container);
  });
});

// 检查滚动位置并更新箭头状态
const checkScrollPosition = (container: Element) => {
  // 检查是否滚动到最左侧
  pageData.isLeftEnd = container.scrollLeft <= 0;

  // 检查是否滚动到最右侧
  const maxScrollLeft = container.scrollWidth - container.clientWidth;
  pageData.isRightEnd = Math.abs(container.scrollLeft - maxScrollLeft) < 5; // 允许5px的误差

  // 计算滚动进度百分比 - 考虑初始可见部分
  if (maxScrollLeft > 0) {
    // 初始可见部分占总宽度的比例
    const initialVisibleRatio = container.clientWidth / container.scrollWidth;
    // 调整后的进度计算，考虑初始可见部分
    const adjustedProgress =
      (container.scrollLeft / maxScrollLeft) * (1 - initialVisibleRatio) * 100 + initialVisibleRatio * 100;
    pageData.scrollProgress = adjustedProgress;
  } else {
    pageData.scrollProgress = 100; // 如果没有可滚动内容，进度为100%
  }
};
</script>
<style scoped lang="scss">
.page-header {
  width: 100%;
  height: 7.34rem;
  margin: 0 auto;
  position: relative;
  object-fit: cover;
  background-size: 100%100%;
  background-image: url("@/assets/icons/quienes-somos/mobile-about-us-bg.png");
  background-repeat: no-repeat;
}

.arrows-container {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 20;
}

.arrows-container img {
  pointer-events: auto;
}

.timeline-container {
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  white-space: nowrap;
  width: 100%;
  -webkit-overflow-scrolling: touch; /* 增强iOS触摸滚动体验 */
  overflow-y: hidden; /* 禁止垂直滚动 */
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

.custom-cursor {
  cursor: url("@/assets/icons/quienes-somos/cursor.ico"), auto !important;
}
.custom-icon {
  position: relative;
  width: 0.34rem;
  height: 0.34rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
}

/* 内圆 */
.custom-icon::after {
  content: "";
  position: absolute;
  width: 0.22rem;
  height: 0.22rem;
  background-color: #e50113;
  border-radius: 50%;
  top: 0.06rem;
  left: 50%;
  transform: translateX(-50%);
}

/* 外圆 */
.custom-icon span {
  position: absolute;
  width: 0.34rem;
  height: 0.34rem;
  border: 0.02rem solid #e50113;
  border-radius: 50%;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}
</style>
