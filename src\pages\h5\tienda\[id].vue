<template>
  <div class="w-full bg-white pt-[0.64rem] pb-[2.4rem] px-[0.4rem]">
    <div class="breadcrumb text-[0.28rem] leading-[0.28rem]">
      <div class="breadcrumb-item">
        <a href="/h5">
          <img loading="lazy" src="@/assets/icons/common/home-black.svg" alt="home" class="home-icon h-[0.28rem]" />
        </a>
      </div>
      <img loading="lazy" src="@/assets/icons/common/arrow-right.svg" alt="arrow" class="arrow-icon" />
      <div class="breadcrumb-item">
        <a href="/h5/tiendas-panoramicas-en-3d">
          <span class="breadcrumb-link">Tiendas panorámicas en 3D</span>
        </a>
      </div>
      <img loading="lazy" src="@/assets/icons/common/arrow-right.svg" alt="arrow" class="arrow-icon" />
      <!-- 当前标题 -->
      <div class="breadcrumb-item">
        <a :href="`/h5/tienda/${pageData.storeDetail.title}`">
          <span class="breadcrumb-link active">
            {{ pageData.storeDetail.title }}
          </span>
        </a>
      </div>
    </div>
    <div class="w-full h-[4.52rem] pt-[0.64rem]">
      <iframe
        style="width: 100%; height: 100%"
        :src="pageData.storeDetail.link"
        allowfullscreen="allowfullscreen"
      ></iframe>
    </div>
    <div class="mt-[0.52rem] text-[0.32rem] leading-[0.44rem] text-[#7F7F7F]">
      <div class="text-[0.64rem] leading-[0.64rem] mb-[0.44rem] text-[#333]">
        {{ pageData.storeDetail.title }}
      </div>
      <div v-if="pageData.storeDetail.title">
        <span class="mr-[0.08rem]">Número de Local:</span>
        <span>{{ pageData.storeDetail.title }}</span>
      </div>
      <div v-if="pageData.storeDetail.product" class="mt-[0.08rem]">
        <span class="mr-[0.08rem]">Producto Principal: </span>
        <span>{{ pageData.storeDetail.product }}</span>
      </div>
      <div v-if="pageData.storeDetail.pubDate" class="mt-[0.08rem]">
        <span class="mr-[0.08rem]">Fecha de publicación: </span>
        <span>{{ pageData.storeDetail.pubDate }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from "vue-router";

const route = useRoute();
const pageData = reactive<any>({
  storeDetail: null,
});

await onPageData();

useHead({
  title: `${pageData.storeDetail.title}-mercado mayorista de Yiwu`,
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/tienda/${route.params.id}/`,
    },
  ],
});


async function onPageData() {
  const res: any = await useWordPressDetail({
    title: route.params.id,
  });
  if (res?.result?.code === 200) {
    pageData.storeDetail = res.data;
  }
}
</script>

<style scoped lang="scss">
.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
}

.home-icon:hover {
  content: url("@/assets/icons/common/home-red.svg");
}

.breadcrumb-link {
  color: #7f7f7f;
  transition: all 0.3s;
  cursor: pointer;
}

.breadcrumb-link:hover,
.breadcrumb-link.active {
  color: #333;
  font-weight: 500;
}

.arrow-icon {
  width: 0.12rem;
  margin: 0 0.16rem;
  filter: brightness(0) saturate(100%) invert(43%) sepia(0%) saturate(0%) hue-rotate(193deg) brightness(96%)
    contrast(90%);
}
</style>
