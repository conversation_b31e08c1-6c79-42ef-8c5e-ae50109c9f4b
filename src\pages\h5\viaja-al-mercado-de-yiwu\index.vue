<template>
  <div class="w-full bg-white">
    <div class="page-header text-[#fff] overflow-auto px-[0.4rem] py-[0.48rem]">
      <div class="absolute top-0 left-0 w-full h-full bg-black opacity-45 z-[1]"></div>
      <div class="relative z-1">
        <div class="flex items-center gap-[0.16rem]">
          <img
            src="@/assets/icons/viaja-al-mercado-de-yiwu/earth.svg"
            alt="viaja al mercado de yiwu"
            class="w-[0.64rem] h-[0.64rem]"
            loading="lazy"
          />
          <div class="text-[0.28rem] leading-[0.28rem]">Descubre el Paraíso de las Importaciones</div>
        </div>
        <div class="text-[0.88rem] leading-[1.32rem] font-medium mt-[0.6rem]">
          ¡Viaja con<br />
          Chilat al Mercado<br />
          de <PERSON><PERSON>!
        </div>
        <div
          @click="onWhatsAppClick"
          class="inline-block py-[0.44rem] px-[0.84rem] text-center cursor-pointer rounded-[1rem] border-[0.02rem] border-[#fff] text-[0.36rem] leading-[0.36rem] mt-[1.32rem] hover:bg-[#fff] hover:text-[#e50113] transition-all duration-300"
        >
          Chatear ahora
        </div>
      </div>
    </div>
    <div class="py-[2rem] px-[0.4rem]">
      <div class="text-[0.56rem] leading-[0.68rem]">¿Qué es el Mercado de Yiwu?</div>
      <div class="text-[0.32rem] leading-[0.48rem] text-[#7F7F7F] mt-[0.36rem]">
        El centro mayorista más grande del mundo, con más de 75,000 puestos y más de 2.1 millones de productos.<br />
        Aquí encuentras de todo: desde juguetes, decoración, papelería, artículos para el hogar, hasta productos de
        temporada y más.
      </div>
      <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mt-[0.6rem]"></div>
      <div class="flex flex-col mt-[0.6rem] gap-[0.64rem]">
        <div v-for="(item, index) in YiwuMarket" :key="index" class="flex items-center gap-[0.4rem] text-center">
          <img :src="item.icon" alt="viaja al mercado de yiwu" class="w-[0.76rem] h-[0.76rem]" loading="lazy" />
          <div class="text-[0.4rem] leading-[0.4rem] font-medium">
            {{ item.title }}
          </div>
          <div class="text-[0.32rem] leading-[0.32rem]">
            {{ item.subTitle }}
          </div>
        </div>
      </div>
    </div>
    <div
      class="rounded-[0.4rem] w-full h-[4.44rem] text-[#fff] pt-[0.56rem] text-center"
      :style="{
        backgroundImage: `url(${redBg})`,
        backgroundSize: '100% 100%',
      }"
    >
      <div class="text-[0.48rem] leading-[0.56rem] font-medium">¿Vendes al por mayor?</div>
      <div class="text-[0.32rem] leading-[0.4rem] mt-[0.32rem]">
        Aquí es tu mejor fuente de productos al mejor precio.
      </div>
      <n-button
        size="large"
        color="#fff"
        text-color="#e50113"
        @click="onWhatsAppClick"
        class="w-[3.84rem] h-[1.28rem] rounded-[10rem] mt-[0.36rem] global-contact-btn"
      >
        <span class="text-[0.32rem] leading-[0.32rem] mr-[0.12rem]">Chatear ahora</span>
        <img
          loading="lazy"
          alt="viaja al mercado de yiwu"
          class="arrow-icon"
          src="@/assets/icons/quienes-somos/arrow-line.svg"
        />
      </n-button>
    </div>
    <div class="w-full px-[0.4rem] py-[1.6rem]">
      <div class="text-[0.56rem] leading-[0.68rem]">¿Qué incluye nuestro servicio?</div>
      <div class="text-[0.32rem] leading-[0.48rem] mt-[0.36rem] text-[#7F7F7F]">
        Todo el proceso, desde el primer día de tu viaje hasta que recibes la mercancía, está en buenas manos.
      </div>
      <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mt-[0.6rem]"></div>
      <div class="mt-[1rem] flex flex-col justify-between relative">
        <!-- 服务列表 -->
        <div
          v-for="(item, index) in consolidationInclusions"
          :key="item.title"
          class="service-item group"
          @click="handleServiceHover(item, index)"
        >
          <div
            class="py-[0.44rem] transition-colors duration-500 cursor-pointer border-b-1 border-[#F2F2F2]"
            :class="[pageData.activeInclusions.includes(item.title) ? 'text-[#e50113]' : '']"
          >
            <div class="flex items-start justify-between gap-[0.28rem]">
              <div class="flex">
                <span
                  class="text-[#999] text-[0.32rem] leading-[0.32rem] mr-[0.28rem] mt-[0.06rem]"
                  :class="[pageData.activeInclusions.includes(item.title) ? 'text-[#e50113]' : '']"
                  >{{ String(index + 1).padStart(2, "0") }}</span
                >
                <span class="text-[0.36rem] leading-[0.44rem]">{{ item.title }}</span>
              </div>
              <img
                v-if="!pageData.activeInclusions.includes(item.title)"
                loading="lazy"
                alt="viaja al mercado de yiwu"
                class="w-[0.44rem] flex-shrink-0"
                src="@/assets/icons/common/expand-red.svg"
              />
              <img
                v-else
                loading="lazy"
                alt="viaja al mercado de yiwu"
                class="w-[0.44rem] flex-shrink-0"
                src="@/assets/icons/common/collapse-red.svg"
              />
            </div>
            <!-- 展开的内容 -->
            <div
              class="w-full overflow-hidden transition-all duration-500 pl-[0.64rem]"
              :class="[
                pageData.activeInclusions.includes(item.title) ? 'mt-[0.2rem]  opacity-100' : 'max-h-0 opacity-0',
              ]"
            >
              <div class="flex flex-col w-full">
                <!-- 服务内容展示 -->
                <div v-for="(child, childIndex) in item.children" :key="childIndex" class="mt-[0.68rem]">
                  <div class="text-[0.36rem] leading-[0.4rem] font-medium text-[#333]">
                    {{ child.title }}
                  </div>
                  <div class="text-[0.32rem] leading-[0.48rem] mt-[0.12rem] text-[#333]">
                    {{ child.desc }}
                  </div>
                  <img
                    loading="lazy"
                    :src="child.icon"
                    alt="viaja al mercado de yiwu"
                    class="w-[6.06rem] rounded-tl-[0.02rem] rounded-tr-[1.6rem] rounded-br-[0.4rem] rounded-bl-[1.6rem] mt-[0.2rem]"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="w-full px-[0.4rem] pb-[1.6rem]">
      <div class="text-[0.56rem] leading-[0.68rem]">¿Quién debería venir?</div>
      <div class="text-[0.32rem] leading-[0.48rem] mt-[0.36rem] text-[#7F7F7F]">
        ¿Compras en Alibaba? En Yiwu puedes ver, tocar y negociar cara a cara, con nuestra asistencia.
      </div>
      <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mt-[0.6rem]"></div>
      <div class="flex flex-col mt-[1.56rem] gap-y-[0.8rem]">
        <div v-for="(item, index) in WhoShouldCome" :key="index" class="flex flex-col items-center gap-y-[0.32rem]">
          <img :src="item.icon" alt="viaja al mercado de yiwu" class="w-[0.76rem] h-[0.76rem]" loading="lazy" />
          <div class="text-[0.36rem] leading-[0.4rem] text-center">
            {{ item.title }}
          </div>
        </div>
      </div>
    </div>
    <div class="h-[15.68rem] px-[0.4rem] pt-[1.6rem] bg-[#F7F7F7] relative overflow-hidden">
      <div class="w-[15.56rem] h-[15.56rem] rounded-full bg-white absolute top-[0.4rem] left-[-2.2rem]"></div>
      <div class="relative z-2">
        <div class="text-[0.56rem] leading-[0.68rem]">Chilat: Tu socio confiable en Yiwu</div>
        <div class="text-[0.32rem] leading-[0.48rem] mt-[0.36rem] text-[#7F7F7F]">
          Miles de importadores latinoamericanos ya están ganando más con nosotros. ¿Te unirás?
        </div>
        <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mt-[0.6rem]"></div>
        <img
          class="w-[6.7rem] rounded-tl-[1.6rem] rounded-tr-[0.4rem] rounded-br-[1.6rem] rounded-bl-[0.04rem] mt-[0.8rem]"
          loading="lazy"
          alt="viaja al mercado de yiwu"
          src="@/assets/icons/viaja-al-mercado-de-yiwu/agent.jpg"
        />
        <div class="flex flex-col gap-[0.8rem] mt-[0.8rem]">
          <div v-for="(info, index) in chilatInfo" :key="index" class="flex items-center gap-[0.4rem]">
            <img loading="lazy" :src="info.icon" alt="viaja al mercado de yiwu" class="w-[0.76rem]" />
            <div class="text-[0.36rem] leading-[0.44rem]">
              {{ info.title }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="rounded-[0.4rem] w-full h-[4.44rem] text-[#fff] pt-[0.36rem] text-center mt-[2rem] mb-[2.4rem]"
      :style="{
        backgroundImage: `url(${redBg})`,
        backgroundSize: '100% 100%',
      }"
    >
      <div class="text-[0.48rem] leading-[0.56rem] font-medium">Contacto rápido</div>
      <div class="text-[0.32rem] leading-[0.4rem] mt-[0.32rem] px-[0.6rem]">
        Haz clic abajo y cuéntanos qué productos buscas. ¡Nosotros te ayudamos a encontrarlos!
      </div>
      <n-button
        size="large"
        color="#fff"
        text-color="#e50113"
        @click="onWhatsAppClick"
        class="w-[3.72rem] h-[1.28rem] rounded-[10rem] mt-[0.36rem] global-contact-btn"
      >
        <span class="text-[0.32rem] leading-[0.32rem] mr-[0.12rem]">Llamar ahora</span>
        <img
          loading="lazy"
          alt="viaja al mercado de yiwu"
          class="arrow-icon"
          src="@/assets/icons/quienes-somos/arrow-line.svg"
        />
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import redBg from "@/assets/icons/quienes-somos/mobile-red-bg.png";
import stalls from "@/assets/icons/viaja-al-mercado-de-yiwu/stalls.svg";
import stock from "@/assets/icons/viaja-al-mercado-de-yiwu/stock.svg";
import exports from "@/assets/icons/viaja-al-mercado-de-yiwu/exports.svg";
import veteran from "@/assets/icons/quienes-somos/veteran.svg";
import branches from "@/assets/icons/quienes-somos/branches.svg";
import multilingual from "@/assets/icons/quienes-somos/multilingual.svg";

import news from "@/assets/icons/viaja-al-mercado-de-yiwu/news.jpg";
import accompany from "@/assets/icons/viaja-al-mercado-de-yiwu/accompany.jpg";
import communication from "@/assets/icons/viaja-al-mercado-de-yiwu/communication.jpg";
import verify from "@/assets/icons/viaja-al-mercado-de-yiwu/verify.jpg";
import streamline from "@/assets/icons/viaja-al-mercado-de-yiwu/streamline.jpg";
import personalization from "@/assets/icons/viaja-al-mercado-de-yiwu/personalization.jpg";
import explorers from "@/assets/icons/viaja-al-mercado-de-yiwu/explorers.svg";
import buyers from "@/assets/icons/viaja-al-mercado-de-yiwu/buyers.svg";
import wholesalers from "@/assets/icons/viaja-al-mercado-de-yiwu/wholesalers.svg";
import innovators from "@/assets/icons/viaja-al-mercado-de-yiwu/innovators.svg";
import threshold from "@/assets/icons/viaja-al-mercado-de-yiwu/threshold.svg";

useHead({
  title: "Cómo llegar al mercado mayorista de Yiwu en China-Chilat",
  meta: [
    {
      name: "description",
      content:
        "Chilat le ayuda a encontrar todo, desde juguetes, decoración del hogar, papelería, artículos domésticos hasta productos de temporada en el mercado de Yiwu.",
    },
  ],
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/viaja-al-mercado-de-yiwu/`,
    },
  ],
});

const YiwuMarket = [
  {
    icon: stalls,
    title: "75K+",
    subTitle: "Puestos comerciales",
  },
  {
    icon: stock,
    title: "2.1M+",
    subTitle: "Productos disponibles",
  },
  {
    icon: exports,
    title: "219",
    subTitle: "Países de exportación",
  },
];

const WhoShouldCome = [
  {
    icon: explorers,
    title: "Importadores primerizos interesados en explorar el mercado chino",
  },
  {
    icon: buyers,
    title: "Empresarios que desean negociar directamente con proveedores chinos",
  },
  {
    icon: wholesalers,
    title: "Compradores con planes de importación a gran escala",
    desc: "Descubre las últimas tendencias del mercado",
  },
  {
    icon: innovators,
    title: "Emprendedores que buscan desarrollar nuevas categorías o alianzas sólidas",
  },
  {
    icon: threshold,
    title: "Compra mínima sugerida: USD 50,000",
  },
];

const chilatInfo = [
  {
    icon: veteran,
    title: "22 años de experiencia",
  },
  {
    icon: multilingual,
    title: "Servicio 100% en español",
  },
  {
    icon: branches,
    title: "Equipo propio en China",
  },
];

const consolidationInclusions = [
  {
    title: "Acompañamiento en tu idioma",
    children: [
      {
        icon: news,
        title: "Traductor español-chino en todo momento",
        desc: "Nuestro equipo habla español y te acompaña en todo momento.",
      },
    ],
  },
  {
    title: "Agenda personalizada",
    children: [
      {
        icon: accompany,
        title: "Sugerencias personalizadas según lo que vendes",
        desc: "Te ayudamos a planear tu viaje, elegir productos y agendar reuniones clave.",
      },
    ],
  },
  {
    title: "Negociación con proveedores",
    children: [
      {
        icon: communication,
        title: "Apoyo en toma de decisiones y cierre de pedidos",
        desc: "Te apoyamos con cotizaciones, selección de productos y gestión de pedidos.",
      },
    ],
  },
  {
    title: "Recepción de mercancías y inspección",
    children: [
      {
        icon: verify,
        title: "Supervisión de calidad",
        desc: "Desde la producción hasta el envío, control de calidad en todo el proceso para garantizar que cada producto cumpla con los estándares.",
      },
    ],
  },
  {
    title: "Consolidación y logística",
    children: [
      {
        icon: streamline,
        title: "Consolidación de compras",
        desc: "Juntamos todos tus productos, revisamos la calidad y organizamos el envío a tu país.",
      },
    ],
  },
  {
    title: "Asesoría en trámites de importación",
    children: [
      {
        icon: personalization,
        title: "Asesoría completa en trámites aduaneros para evitar problemas en la importación",
        desc: "Te guiamos en toda la documentación: facturas, packing list y más, para que importes sin complicaciones.",
      },
    ],
  },
];

onMounted(() => {
  handleServiceHover(consolidationInclusions[0], 0);
});

const pageData = reactive({
  activeInclusions: [] as string[],
});

const handleServiceHover = (item: any, _index: number) => {
  const title = item.title;
  const currentIndex = pageData.activeInclusions.indexOf(title);

  if (currentIndex === -1) {
    // 未展开则添加
    pageData.activeInclusions.push(title);
  } else {
    // 已展开则移除
    pageData.activeInclusions.splice(currentIndex, 1);
  }
};
</script>
<style scoped lang="scss">
.page-header {
  width: 100%;
  height: 11.6rem;
  position: relative;
  object-fit: cover;
  background-size: 100%100%;
  background-image: url("@/assets/icons/viaja-al-mercado-de-yiwu/mobile-header-bg.jpg");
  background-repeat: no-repeat;
}

.guide-wrapper {
  &:hover {
    color: #e50113;
    img {
      content: url("@/assets/icons/viaja-al-mercado-de-yiwu/guide-active.svg");
    }
  }
}
.video-wrapper {
  position: relative;
  &:hover {
    color: #25d366;
    img {
      content: url("@/assets/icons/viaja-al-mercado-de-yiwu/video-active.svg");
    }
  }
  &::after {
    content: "";
    position: absolute;
    top: 50%;
    left: -0.76rem;
    transform: translateY(-50%);
    width: 0.02rem;
    height: 2.2rem;
    background-color: #d9d9d9;
  }
}

.whatsapp-wrapper {
  position: relative;
  &:hover {
    color: #25d366;
    img {
      content: url("@/assets/icons/common/whatsapp-highlight.svg");
    }
  }
  &::after {
    content: "";
    position: absolute;
    top: 50%;
    left: -0.76rem;
    transform: translateY(-50%);
    width: 0.02rem;
    height: 2.2rem;
    background-color: #d9d9d9;
  }
}

// 过渡动画样式
.fade-slide-enter-active {
  transition: opacity 0.3s ease-in-out;
}

.fade-slide-leave-active {
  transition: none;
}

.fade-slide-enter-from {
  opacity: 0;
}

.fade-slide-leave-to {
  opacity: 0;
}

.fade-slide-enter-to,
.fade-slide-leave-from {
  opacity: 1;
}

// 图片切换动画
.image-fade-enter-active {
  transition: opacity 0.3s ease-in-out;
}

.image-fade-leave-active {
  transition: none;
}

.image-fade-enter-from {
  opacity: 0;
}

.image-fade-leave-to {
  opacity: 0;
}

.image-fade-enter-to,
.image-fade-leave-from {
  opacity: 1;
}
</style>
