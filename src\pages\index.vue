<template>
  <div class="w-[1280px] mx-auto bg-white pb-[80px]">
    <n-carousel
      :draggable="true"
      :autoplay="true"
      :show-dots="true"
      :interval="8000"
      class="homepage-carousel"
      dot-placement="bottom"
      dot-type="line"
      :show-arrow="false"
      trigger="hover"
    >
      <n-carousel-item>
        <div class="page-header px-[38px] py-[32px] flex justify-between">
          <div class="w-[628px] text-[76px] leading-[102px] font-medium">¿Cómo importar desde China?</div>
          <div class="mt-[82px]">
            <div class="flex items-center cursor-pointer" @click="onOpenVideo()">
              <span>Conócenos Aquí</span>
              <div class="w-[8px] h-[8px] rounded-full border border-[#e50113] ml-[12px] mr-[16px]"></div>
              <img loading="lazy" src="@/assets/icons/home/<USER>" alt="video" class="w-[102px] h-[102px]" />
            </div>
            <div class="w-[228px] text-[18px] leading-[30px] mt-[88px] text-right relative">
              <img
                loading="lazy"
                src="@/assets/icons/home/<USER>"
                alt="stroke"
                class="w-[32px] absolute top-[-6px] right-[172px]"
              />
              <span>Desde encontrar los productos hasta el despacho, le brindamos una solución completa.</span>
            </div>
            <div class="text-[20px] leading-[20px] mt-[24px] text-right">—— Chilat</div>
          </div>
        </div>
      </n-carousel-item>
      <!-- 第二个轮播项 -->
      <n-carousel-item>
        <div class="second-carousel-wrapper text-white text-center">
          <a href="/tiendas-panoramicas-en-3d" class="w-full h-full block pt-[78px] px-[214px]">
            <div class="text-[76px] leading-[102px]">Tiendas panorámicas en</div>
            <div class="text-[90px] leading-[102px]">3D</div>
            <div class="text-[32px] leading-[32px] mt-[48px]">Miles de productos presentados online</div>
          </a>
          <div class="w-[200px] h-[50px] absolute bottom-[40px] left-1/2 -translate-x-1/2"></div>
        </div>
      </n-carousel-item>
    </n-carousel>

    <div class="pt-[140px] px-[38px]">
      <div class="text-[34px] leading-[34px] text-center">Usted necesita</div>
      <div class="w-[50px] h-[2px] bg-[#e50113] mx-auto mt-[36px]"></div>
      <div class="flex justify-between flex-wrap gap-y-[30px] mt-[78px]">
        <a
          v-for="(item, index) in requirementInfo"
          :href="item.path"
          :key="index"
          class="group w-[386px] h-[315px] border border-[#EBEBEB] rounded-[20px] px-[46px] pt-[26px] hover:border-[#E50113] transition-all duration-300"
        >
          <img loading="lazy" :src="item.icon" :alt="item.title" class="w-[56px] h-[56px]" />
          <div class="w-[294px] mx-auto text-[20px] leading-[22px] text-[#333333] mt-[18px] h-[44px] flex items-center">
            {{ item.title }}
          </div>
          <div
            class="text-[14px] leading-[16.8px] text-[#7F7F7F] mt-[13px] flex flex-col gap-[8px] group-hover:text-[#333] transition-all duration-300"
          >
            <span v-for="(desc, index) in item.content" :key="index">
              {{ desc }}
            </span>
          </div>
        </a>
      </div>
    </div>
    <div class="pt-[140px] px-[38px]">
      <div class="text-[34px] leading-[34px] text-center">Elíjanos</div>
      <div class="w-[50px] h-[2px] bg-[#e50113] mx-auto mt-[36px]"></div>
      <div class="flex justify-between flex-wrap gap-y-[30px] mt-[78px]">
        <a v-for="(item, index) in chooseInfo" :key="index" :href="item.path" class="w-[380px] group">
          <div class="w-full overflow-hidden rounded-[20px]">
            <img
              loading="lazy"
              :src="item.icon"
              :alt="item.alt"
              class="w-[380px] group-hover:scale-125 transition-transform duration-800"
            />
          </div>

          <div class="flex gap-[16px] items-center mt-[30px]">
            <img
              loading="lazy"
              src="@/assets/icons/home/<USER>"
              :alt="item.alt"
              class="w-[25px] h-[25px] group-hover:scale-150 transition-transform duration-800"
            />
            <div
              class="w-[280px] text-[20px] leading-[22px] text-[#333333] h-[44px] flex items-center group-hover:text-[#000] group-hover:translate-x-[6px] transition-all !duration-800"
            >
              {{ item.title }}
            </div>
          </div>

          <div
            class="text-[16px] leading-[20.8px] text-[#7F7F7F] mt-[18px] flex flex-col gap-[8px] group-hover:text-[#333] transition-all duration-800"
          >
            <span v-for="(desc, index) in item.content" :key="index">
              {{ desc }}
            </span>
          </div>
        </a>
      </div>
    </div>
    <div class="pt-[140px] pr-[18px]">
      <div class="text-[34px] leading-[34px] text-center">Nuestros servicios</div>
      <div class="w-[50px] h-[2px] bg-[#e50113] mx-auto mt-[36px]"></div>
      <div class="mt-[78px] flex justify-between relative min-h-[700px]">
        <!-- 左侧图片区域 -->
        <div class="w-[584px] h-[700px] relative">
          <img
            loading="lazy"
            src="@/assets/icons/home/<USER>"
            alt="service-bg"
            class="w-[420px] h-[700px] object-cover rounded-[20px] absolute bottom-[0] left-[0] z-1"
          />
          <div class="relative h-full">
            <transition-group name="service-image">
              <div
                v-for="item in serviceInfo"
                :key="item.title"
                class="absolute left-[116px] w-[462px] transition-all duration-500 z-2"
                :class="[pageData.activeService?.title === item.title ? 'opacity-100 visible' : 'opacity-0 invisible']"
                :style="{
                  transform: `translateY(${pageData.imagePosition}px)`,
                }"
              >
                <img
                  loading="lazy"
                  :src="item.icon"
                  :alt="item.alt"
                  class="w-[462px] h-[290px] object-cover rounded-[20px]"
                />
              </div>
            </transition-group>
          </div>
        </div>

        <!-- 右侧服务列表 -->
        <div class="w-[584px] relative mt-[86px] min-h-[700px]">
          <div
            v-for="(item, index) in serviceInfo"
            :key="item.title"
            class="service-item group"
            @mouseenter="handleServiceHover(item, index)"
          >
            <div
              class="p-[20px] transition-colors duration-300 cursor-pointer"
              :class="[pageData.activeService?.title === item.title ? 'text-[#e50113]' : '']"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <span class="text-[#999] text-[16px] mr-[72px]">{{ String(index + 1).padStart(2, "0") }}</span>
                  <span class="text-[22px] leading-[22px]">{{ item.title }}</span>
                </div>
                <img
                  v-if="pageData.activeService?.title !== item.title"
                  loading="lazy"
                  alt="expand"
                  class="w-[22px]"
                  src="@/assets/icons/common/expand-red.svg"
                />
                <img
                  v-else
                  loading="lazy"
                  alt="collapse"
                  class="w-[22px]"
                  src="@/assets/icons/common/collapse-red.svg"
                />
              </div>
              <!-- 展开的内容 -->
              <div
                class="overflow-hidden transition-all duration-300 flex gap-[14px] ml-[90px] mr-[22px]"
                :class="[
                  pageData.activeService?.title === item.title
                    ? 'mt-[22px]  max-h-[200px] opacity-100'
                    : 'max-h-0 opacity-0',
                ]"
              >
                <div class="text-[16px] leading-[22px] text-[#666] w-[378px]">
                  {{ item.content }}
                </div>
                <a :href="item.path" class="flex items-center">
                  <img
                    loading="lazy"
                    src="@/assets/icons/quienes-somos/arrow-line.svg"
                    alt="arrow"
                    class="w-[70px] transition-transform duration-300 flex-shrink-0 arrow-icon"
                    :class="[pageData.activeService?.title === item.title ? 'translate-x-1' : '']"
                  />
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="pt-[54px] px-[38px] flex justify-between">
      <div>
        <div class="rounded-[20px] w-[584px] h-[328.5px] overflow-hidden bg-white">
          <video-you-tube
            :width="584"
            :height="328.5"
            :poster="ourAdvantage"
            youtubeId="7c9B2YtloiM"
            title="Nuestra ventaja"
            titleCh="我们的优势"
          ></video-you-tube>
        </div>
        <div class="text-[24px] leading-[24px] mt-[28px]">Nuestra ventaja</div>
      </div>
      <div>
        <div class="rounded-[20px] w-[584px] h-[328.5px] overflow-hidden bg-white">
          <video-you-tube
            :width="584"
            :height="328.5"
            :poster="ourProcess"
            youtubeId="iPccIsBMF1k"
            title="Como trabajamos"
            titleCh="我们如何工作"
          ></video-you-tube>
        </div>
        <div class="text-[24px] leading-[24px] mt-[28px]">Cómo trabajarnos</div>
      </div>
    </div>
    <div class="pt-[140px] px-[38px]">
      <div class="text-[34px] leading-[34px]">Nuestro equipo</div>
      <div class="w-[50px] h-[2px] bg-[#e50113] mt-[30px]"></div>
      <div class="text-[18px] leading-[21.6px] text-[#7F7F7F] mt-[32px] w-[1124px]">
        <div>
          En Chilat contamos con un equipo de más de 80 profesionales en Yiwu y Guangzhou, incluyendo 40 expertos
          hispanohablantes, dedicados a facilitar la importación desde China hacia países de Latinoamérica.
        </div>
        <div class="mt-[8px]">
          Nuestra experiencia nos permite apoyar a pymes y grandes corporaciones, gestionando compras, control de
          calidad y logística internacional. Hemos colaborado con marcas reconocidas como Carrefour, Disney, Jumbo,
          India Style, Cencosud y Alibaba, garantizando soluciones eficaces para negocios de cualquier escala.
        </div>
      </div>
      <div class="flex justify-between mt-[56px] relative z-3 custom-cursor cursor-gap">
        <a v-for="item in teamInfo" :key="item.name" :href="item.path" class="custom-cursor">
          <div class="relative w-[278px] team-item rounded-[20px] overflow-hidden">
            <img loading="lazy" :src="item.image" :alt="item.name" class="w-[278px]" />
            <div class="absolute bottom-0 left-0 w-full z-1 text-[#fff] px-[20px] pb-[20px] team-content">
              <div class="text-[28px] leading-[28px] mb-[12px]">
                {{ item.name }}
              </div>
              <div class="team-desc text-[16px] leading-[21.6px]">
                {{ item.position }}
              </div>
            </div>
          </div>
        </a>
      </div>
      <a class="flex justify-center mt-[94px]" href="/quienes-somos/">
        <n-button
          size="large"
          color="#fff"
          text-color="#e50113"
          class="global-navigation-btn w-[228px] rounded-[500px] h-[50px]"
        >
          <span class="text-[18px] leading-[18px]">Conócenos</span>
        </n-button>
      </a>
    </div>
    <div class="pt-[140px] pb-[80px] px-[38px]">
      <div class="text-[34px] leading-[34px] text-center">Testimonials</div>
      <div class="w-[50px] h-[2px] bg-[#e50113] mx-auto mt-[36px]"></div>
      <div class="mt-[78px] relative px-[80px] select-none">
        <n-carousel
          ref="carousel"
          effect="card"
          prev-slide-style="transform: translateX(-127%) translateZ(-432px);"
          next-slide-style="transform: translateX(27%) translateZ(-432px);"
          style="height: 450px"
          :show-dots="false"
          :current-index="currentIndex"
          :centered-slides="true"
          :loop="true"
          @update:current-index="currentIndex = $event"
          class="z-11"
        >
          <n-carousel-item
            :style="{ width: '60%', cursor: 'pointer' }"
            v-for="(item, index) in feedbackInfo"
            :key="item.title"
            @click="carousel?.to(index)"
          >
            <div
              class="w-[450px] h-[450px] rounded-full flex flex-col items-center text-[#fff] pt-[46px] testimonial-card mx-auto"
              :style="`background: ${item.bgColor};`"
            >
              <img loading="lazy" :src="item.icon" class="w-[78px] h-[78px]" />
              <div class="text-[34px] leading-[34px] mt-[12px]">
                {{ item.title }}
              </div>
              <div class="text-[14px] leading-[14px] mt-[10px]">
                {{ item.subTitle }}
              </div>
              <div class="text-[16px] leading-[17.6px] mt-[18px] w-[355px] text-center">
                {{ item.content }}
              </div>
            </div>
          </n-carousel-item>
        </n-carousel>
        <div class="absolute left-0 top-1/2 -translate-y-1/2 z-10 flex justify-between w-full">
          <img
            loading="lazy"
            alt="prev"
            @click="carousel?.prev()"
            src="@/assets/icons/home/<USER>"
            class="w-[70px] h-[70px] cursor-pointer rotate-180"
          />
          <img
            loading="lazy"
            alt="next"
            @click="carousel?.next()"
            src="@/assets/icons/home/<USER>"
            class="w-[70px] h-[70px] cursor-pointer"
          />
        </div>
      </div>
    </div>
    <WhatsAppContact :showWhatsAppCtaBanner="false" />
  </div>
  <video-modal ref="videoModalRef"></video-modal>
</template>

<script setup lang="ts">
import step1 from "@/assets/icons/home/<USER>";
import step2 from "@/assets/icons/home/<USER>";
import step3 from "@/assets/icons/home/<USER>";
import step4 from "@/assets/icons/home/<USER>";
import step5 from "@/assets/icons/home/<USER>";
import step6 from "@/assets/icons/home/<USER>";
import productQuality from "@/assets/icons/home/<USER>";
import fullConsolidation from "@/assets/icons/home/<USER>";
import translationSupport from "@/assets/icons/home/<USER>";
import ourAdvantage from "@/assets/icons/home/<USER>";
import ourProcess from "@/assets/icons/home/<USER>";
import manager1 from "@/assets/icons/home/<USER>";
import manager2 from "@/assets/icons/home/<USER>";
import manager3 from "@/assets/icons/home/<USER>";
import manager4 from "@/assets/icons/home/<USER>";
import fabian from "@/assets/icons/home/<USER>";
import gerardo from "@/assets/icons/home/<USER>";
import maria from "@/assets/icons/home/<USER>";
import service1 from "@/assets/icons/home/<USER>";
import service2 from "@/assets/icons/home/<USER>";
import service3 from "@/assets/icons/home/<USER>";
import service4 from "@/assets/icons/home/<USER>";
import service5 from "@/assets/icons/home/<USER>";
import service6 from "@/assets/icons/home/<USER>";
import service7 from "@/assets/icons/home/<USER>";
import service8 from "@/assets/icons/home/<USER>";

useHead({
  title: "Agente de compras en China Importar desde China a Latinoamérica-Chilat",
  meta: [
    {
      name: "description",
      content:
        "Chilat, el mayor agente de compras en China para Latinoamérica.Ayudamos a importar productos al por mayor: electrónica, ropa, juguetes, artículos para el hogar y más, con asesoría en español.",
    },
  ],
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/`,
    },
    {
      rel: "alternate",
      media: "only screen and (max-width: 640px)",
      href: `https://www.chilat.com/h5/`,
    },
  ],
});

import { ref, onMounted, reactive } from "vue";
import type { CarouselInst } from "naive-ui";

const videoModalRef = ref<any>(null);
const carousel = ref<CarouselInst | null>(null);
const currentIndex = ref(0);

interface ServiceItem {
  icon: string;
  title: string;
  content: string;
}

const requirementInfo = [
  {
    icon: step1,
    path: "/servicios/#asesoria-de-compras",
    title: "Comparar Agencias de Compras en China",
    content: [
      "¿Cuál es la agencia de compras en China más confiable para Latinoamérica?",
      "¿Qué empresa me ofrece mejor costo y seguridad en importaciones?",
      "¿Quién me ayuda a desarrollar mi negocio mayorista con productos chinos?",
    ],
  },
  {
    icon: step2,
    path: "/servicios/#busqueda-de-proveedores-y-productos",
    title: "Encontrar Productos y Proveedores Confiables en China",
    content: [
      "¿Cómo identificar fábricas directas en China?",
      "¿Dónde obtener productos de calidad al por mayor?",
      "¿Cómo acceder a nuevos productos competitivos del mercado chino?",
    ],
  },
  {
    icon: step3,
    path: "/servicios#viajes-de-negocios-a-china/",
    title: "Asistencia en su Viaje de Negocios a China",
    content: [
      "¿Quién me ayuda con transporte, hotel y traducción en China?",
      "No conozco China, ¿cómo puedo contar con un guía local?",
      "No hablo chino ni inglés, ¿cómo manejar mis negociaciones?",
    ],
  },
  {
    icon: step4,
    path: "/viajar-a-china/",
    title: "Cómo Importar desde China a Latinoamérica",
    content: [
      "¿Dónde puedo comprar productos al por mayor en China?",
      "¿Cómo enviar los productos de China a mi país de forma segura?",
      "¿Qué pasos seguir para despachar mercancías en la aduana?",
    ],
  },
  {
    icon: step5,
    path: "/servicios#control-de-calidad/",
    title: "Verificar Proveedores Chinos y Controlar la Calidad",
    content: [
      "¿Cómo saber si un proveedor en China es realmente confiable?",
      "¿Qué garantías tengo para proteger mi inversión en compras?",
      "¿Cómo asegurar el control de calidad antes del envío?",
    ],
  },
  {
    icon: step6,
    path: "/consolidacion-de-cargas-y-logistica/",
    title: "Consolidación de Productos y Despacho Aduanero",
    content: [
      "¿Se puede importar con marca propia desde China?",
      "¿Quién me ayuda a pagar a los proveedores en China?",
      "¿Cómo consolidar diferentes productos en un solo envío?",
    ],
  },
];

const chooseInfo = [
  {
    icon: productQuality,
    path: "/elijanos#productos-y-calidad",
    title: "Productos Competitivos y Control de Calidad",
    alt: "Productos competitivos y control de calidad en importaciones desde China a Latinoamérica",
    content: [
      "Le ayudamos a importar los productos más competitivos y de alta calidad desde China para Latinoamérica.",
      "Nuestro equipo realiza inspección en fábrica y control de calidad antes del despacho, garantizando seguridad en sus compras mayoristas.",
    ],
  },
  {
    icon: fullConsolidation,
    path: "/elijanos#consolidaciones-y-contenedor-completo",
    title: "Consolidación de Compras y Envío en Contenedor Completo",
    alt: "Consolidación de productos y envío en contenedor completo desde China para mayoristas de Latinoamérica",
    content: [
      "Optimizamos su logística al consolidar productos de diferentes proveedores en un mismo envío desde China.",
      "Ofrecemos tanto consolidación de cargas como contenedor completo (FCL), reduciendo costos y facilitando la importación a su país.",
    ],
  },
  {
    icon: translationSupport,
    path: "/elijanos#acompanamiento-y-traduccion",
    title: "Acompañamiento en China y Servicios de Traducción",
    alt: "Acompañamiento en China y servicios de traducción para importadores mayoristas de Latinoamérica",
    content: [
      "Somos su socio local en China: ofrecemos acompañamiento en visitas a fábricas, ferias y mercados mayoristas como Yiwu.",
      "Nuestro equipo brinda traducción profesional en español, chino e inglés, resolviendo barreras de idioma y diferencias culturales en cada negociación.",
    ],
  },
];

const serviceInfo = [
  {
    icon: service1,
    path: "/servicios/#asesoria-de-compras",
    title: "Asesoría de compras",
    alt: "Asesoría de compras para importar productos desde China a Latinoamérica",
    content:
      "Brindamos asesoría experta para importadores de Latinoamérica que desean comprar productos directamente desde China. Nuestro equipo hispanohablante te guía en cada etapa de negociación y compra.",
  },
  {
    icon: service2,
    path: "/servicios/#busqueda-de-proveedores-y-productos",
    title: "Búsqueda de proveedores y productos",
    alt: "Búsqueda de proveedores confiables y productos mayoristas en China para Latinoamérica",
    content:
      "Localizamos proveedores confiables y productos de calidad en mercados como Yiwu, Guangzhou y Shenzhen. Te ofrecemos acceso directo a fábricas chinas con precios competitivos.",
  },
  {
    icon: service3,
    path: "/servicios/#validacion-de-fabricantes",
    title: "Validación de fabricantes",
    alt: "Validación de fabricantes chinos y verificación de fábricas para importaciones seguras",
    content:
      "Si ya ha contactado con un proveedor o cotizados, verificamos la autenticidad de los fabricantes y evaluamos su capacidad de producción antes de realizar pedidos, para garantizar que trabaje solo con proveedores de confianza en China.",
  },
  {
    icon: service4,
    path: "/servicios/#viajes-de-negocios-a-china",
    title: "Viajes de negocios a China",
    alt: "Viajes de negocios a China con acompañamiento y guía para importadores de Latinoamérica",
    content:
      "Organizamos visitas a fábricas y ferias comerciales en China para facilitar negociaciones presenciales y la selección de proveedores de confianza.",
  },
  {
    icon: service5,
    path: "/viajar-a-china/",
    title: "Agente de compras",
    alt: "Agente de compras en China especializado en importación mayorista para Latinoamérica",
    content:
      "Actuamos como tu agente de compras en China, gestionando negociación, control de calidad, consolidación de cargas y envío internacional hasta tu país.",
  },
  {
    icon: service6,
    path: "/servicios/#control-de-calidad",
    title: "Control de calidad",
    alt: "Control de calidad de productos en fábricas chinas antes del envío a Latinoamérica",
    content:
      "Ahorre dinero y evitar defectos costosos, garantizando que sus importaciones de productos cumplan con sus especificaciones y estándares de calidad.",
  },
  {
    icon: service7,
    path: "/consolidacion-de-cargas-y-logistica/",
    title: "Consolidación de cargas y logística",
    alt: "Consolidación de cargas y logística de importación desde China para mayoristas",
    content:
      "Ofrecemos consolidación de mercancías de múltiples proveedores en un solo envío, optimizando costos y tiempos. Gestionamos todo el transporte internacional y trámites aduaneros.",
  },
  {
    icon: service8,
    path: "/servicios/#fotos-del-producto-y-catalogo",
    title: "Fotos del producto y catálogo",
    alt: "Fotos de productos y catálogo mayorista para importar desde China a Latinoamérica",
    content:
      "Proporcionamos fotografías profesionales y catálogos actualizados de los productos, por lo que puede venderlos con catálogo y fotos antes de recibir las mercancías.",
  },
];
const teamInfo = [
  {
    image: manager2,
    name: "Mindy Wang",
    path: "/chilat-team/#Mindy",
    position: "CEO y Presidente del Grupo Chilat, Gerente General en MingZhan Trade Import & Export Co.,LTD.",
  },
  {
    image: manager1,
    name: "Andy Ren",
    path: "/chilat-team/#Andy",
    position: "CEO y Presidente del Grupo Chilat, Director General MingZhan Trade Import & Export Co.,LTD.",
  },
  {
    image: manager3,
    name: "Irene",
    path: "/chilat-team/#Irene",
    position: "Gerente de cliente",
  },
  {
    image: manager4,
    name: "Elisa",
    path: "/chilat-team/#Elisa",
    position: "Gerente de cliente exterior de Chilat",
  },
];

const feedbackInfo = [
  {
    icon: gerardo,
    bgColor: "#008668",
    title: "Gerardo",
    subTitle: "Cliente de Argentina",
    content:
      "“ Primero te agradezco por el servicio que me has dado a mi empresa a la fecha ya sea por recibir en tiempo la mercadería demostrando el profesionalismo en los controles de calidad cantidad tratada acompañando en todos lo embarques la documentación requerida por las leyes en argentina . Y decirte gracias Chilat por ser eficientes y por tener un buen personal calificado y muy profesional. ”",
  },
  {
    icon: maria,
    bgColor: "#E1523D",
    title: "María",
    subTitle: "Cliente de Colombia",
    content:
      "“ Hace 12 años trabajo con la empresa de la señora Mindy importó desde china para colombia muchos productos decorativos y ha sido un éxito para nu será empresa contar con un agente tan serio ,responsable y excelente manejo de dinero y nuestra carga llega a nuestro destino con toda la documentación y pedido solicitado son excelentes como compañía ampliamente los recomendamos. Suspendisse commodo ullamcorper magna egestas sem. ”",
  },
  {
    icon: fabian,
    bgColor: "#3D74AC",
    title: "Fabián",
    subTitle: "Clientes de Uruguay",
    content:
      "“ Agradecemos a laempresa Chilat y karmen,que nos brindaron la confianza para a hacer nuestra primer importación desde China.Gracias a la empresa@chilat por hacer un sueño realidad! ”",
  },
];

const serviceItemHeight = 64; // 每个服务项的高度
const imageHeight = 290; // 图片高度
const pageData = reactive({
  activeService: serviceInfo[0],
  imagePosition: 0,
});

onMounted(() => {
  handleServiceHover(serviceInfo[0], 0);
});

const handleServiceHover = (item: any, index: number) => {
  pageData.activeService = item;
  // 计算图片的基础位置（默认对齐服务项）
  let basePosition = index * serviceItemHeight + (serviceItemHeight - imageHeight) / 2 + 200;
  // 计算图片底部位置
  const imageBottom = basePosition + imageHeight;

  // 处理靠近底部的情况，防止图片超出 700为左侧图片区域高度
  if (imageBottom > 700) {
    pageData.imagePosition = 700 - imageHeight; // 预留一点间距
  } else {
    pageData.imagePosition = basePosition;
  }
};

function onOpenVideo() {
  const video = {
    id: "JYZdQqGFCeM",
    title: "Conócenos Aquí",
  };
  if (videoModalRef.value) {
    videoModalRef.value.onOpenVideo(video);
  }
}
</script>
<style scoped lang="scss">
.page-header {
  width: 100%;
  height: 538px;
  margin: 0 auto;
  position: relative;
  object-fit: cover;
  background-size: 100%100%;
  background-image: url("@/assets/icons/home/<USER>");
  background-repeat: no-repeat;
  border-radius: 0px 0px 20px 20px;
}

.second-carousel-wrapper {
  width: 100%;
  height: 538px;
  margin: 0 auto;
  position: relative;
  object-fit: cover;
  background-size: 100%100%;
  background-image: url("@/assets/icons/home/<USER>");
  background-repeat: no-repeat;
  border-radius: 0px 0px 20px 20px;
}

.team-item {
  border-radius: 20px;
  transition: all 0.5s ease;
  transform-origin: center center;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 20px;
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.2) 100%);
    opacity: 0;
    transition: opacity 0.5s ease;
    z-index: 1;
  }
  &:hover {
    transform: scale(1.09);
    z-index: 1;
    &::before {
      opacity: 1;
    }
    .team-content {
      bottom: 20px;
    }
    .team-desc {
      max-height: 84px;
    }
  }
  .team-content {
    transition: bottom 0.5s ease;
    z-index: 2;
  }
}
.team-desc {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.5s ease;
}
.custom-cursor {
  cursor: url("@/assets/icons/home/<USER>"), auto !important;
}
:deep(.n-carousel.n-carousel--card .n-carousel__slide.n-carousel__slide--prev) {
  opacity: 1;
}
:deep(.n-carousel.n-carousel--card .n-carousel__slide.n-carousel__slide--next) {
  opacity: 1;
}

.service-image-enter-active,
.service-image-leave-active {
  transition: all 0.5s ease;
}

.service-image-enter-from,
.service-image-leave-to {
  opacity: 0;
}

.service-image-enter-to,
.service-image-leave-from {
  opacity: 1;
}

:deep(.service-image-move) {
  transition: transform 0.5s ease;
}

.service-item {
  border-bottom: 1px solid #ebebeb;

  // &:last-child {
  //   border-bottom: none;
  // }
}

// 优化过渡动画
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

.arrow-icon {
  &:hover {
    content: url("@/assets/icons/quienes-somos/arrow-red.svg");
    transform: scale(1.1);
  }
}
:deep(.homepage-carousel.n-carousel.n-carousel--bottom .n-carousel__dots) {
  bottom: 64px;
  z-index: 10;
  .n-carousel__dot {
    width: 62px;
    height: 2px;
    margin: 0 9px;
  }
}
</style>
