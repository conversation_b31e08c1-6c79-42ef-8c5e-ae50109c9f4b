<template>
  <div class="w-[1280px] mx-auto bg-white">
    <div class="page-header text-[#fff] overflow-auto px-[24px] py-[50px]">
      <Breadcrumb :items="breadcrumbItems" />
      <div class="text-[80px] leading-[80px] font-medium mt-[40px]">
        Quiénes somos
      </div>
    </div>
    <div class="pt-[120px] pb-[100px] px-[38px]">
      <div class="text-[34px] leading-[34px] font-medium text-center">
        ¿Qué es Chilat?
      </div>
      <div
        class="text-[18px] leading-[22px] mt-[16px] text-center text-[#7F7F7F]"
      >
        Somos el agente de compra y abastecimiento más grande de China dedicado
        a servir a importadores de habla hispana. <br />
        Brindamos a nuestros clientes un conjunto de soluciones para todas las
        importaciones desde China.
      </div>
      <div class="w-[50px] h-[3px] bg-[#e50113] mt-[36px] mx-auto"></div>
      <div class="mt-[78px] flex items-center">
        <div
          v-for="(info, index) in chilatInfo"
          :key="index"
          class="flex items-center"
          :class="{
            'pr-[36px] mr-[36px] border-r-1 border-[#E6E6E6]':
              index !== chilatInfo.length - 1,
          }"
        >
          <img
            loading="lazy"
            :src="info.icon"
            alt="quienes somos"
            class="w-[56px] mr-[20px]"
          />
          <div class="w-[276px] text-[20px] leading-[22px]">
            {{ info.title }}
          </div>
        </div>
      </div>
    </div>
    <div class="py-[56px] bg-[#FAFAFA] text-center">
      <div class="text-[33px] leading-[34px] font-medium">
        Nuestros servicio
      </div>
      <div class="w-[50px] h-[3px] bg-[#e50113] mt-[36px] mx-auto"></div>
      <div class="flex flex-wrap mt-[30px]">
        <a
          v-for="(service, index) in serviceInfo"
          :key="index"
          :href="service.path"
          class="block w-[320px] h-[200px] flex flex-col items-center px-[42px] py-[40px] hover:bg-[#F2F2F2] transition-all duration-300 cursor-pointer"
          @mouseover="pageData.serviceIndex = index"
          @mouseleave="pageData.serviceIndex = -1"
        >
          <img
            loading="lazy"
            class="w-[66px]"
            alt="quienes somos"
            :src="service.icon"
          />
          <div class="text-[20px] leading-[20px] mt-[14px]">
            {{ service.title }}
          </div>
        </a>
      </div>
    </div>
    <div class="py-[100px] px-[38px]">
      <div class="text-[34px] leading-[34px] font-medium text-center">
        ¿Qué podemos hacer por usted?
      </div>
      <div class="w-[50px] h-[3px] bg-[#e50113] mt-[36px] mx-auto"></div>
      <div class="mt-[30px]">
        <div
          class="mx-[20px] py-[26px] border-b-1 border-[#F2F2F2]"
          v-for="(support, index) in supportInfo"
          :key="index"
        >
          <span class="text-[16px] leading-[16px] text-[#7F7F7F] mr-[92px]"
            >0{{ index + 1 }}</span
          >
          <span class="text-[18px] leading-[22px]">{{ support }}</span>
        </div>
      </div>
      <div class="flex justify-center mt-[64px]">
        <n-button
          size="large"
          color="#fff"
          text-color="#e50113"
          @click="onWhatsAppClick"
          class="support-btn w-[506px] rounded-[500px] h-[50px]"
        >
          <span class="text-[18px] leading-[18px]">
            ¡Necesito un agente que me de soporte ahora mismo!
          </span>
        </n-button>
      </div>
    </div>
    <div class="pt-[100px] pb-[120px] px-[38px]">
      <div class="text-[34px] leading-[34px] font-medium">Nuestra ventaja</div>
      <div class="mt-[50px] flex items-center">
        <div
          class="flex-shrink-0 pr-[40px] mr-[40px] border-r-1 border-[#E6E6E6]"
        >
          <div class="w-[536px] h-[301px] rounded-[20px] overflow-hidden">
            <video-you-tube
              :width="536"
              :height="301"
              youtubeId="7c9B2YtloiM"
              titleCh="您企业的最佳伙伴#购买中国#义乌"
              poster="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/03/14/6ba06646-8108-40a6-8f8c-d6f994b649ea.png"
            ></video-you-tube>
          </div>
        </div>

        <div>
          <div class="text-[16px] leading-[22px] text-[#7F7F7F]">
            A diferencia de otras empresas multilingües, como empresa de 22 años
            enfocada en el comercio exterior español, tenemos muchas ventajas
            únicas. Por favor, mire el video a continuación para más detalles.
          </div>
          <n-button
            size="large"
            color="#fff"
            text-color="#e50113"
            @click="onWhatsAppClick"
            class="support-btn w-[228px] rounded-[500px] mt-[54px] h-[50px]"
          >
            <span class="text-[18px] leading-[18px]"> ¡Consultar ahora! </span>
          </n-button>
        </div>
      </div>
    </div>
    <div class="pt-[120px] pl-[38px]">
      <div class="text-[34px] leading-[34px] font-medium text-center">
        Nuestra oficina
      </div>
      <div
        class="text-[16px] leading-[22px] text-[#7F7F7F] text-center mt-[16px]"
      >
        A medida que la compañía continúa creciendo, hemos experimentado cuatro
        generaciones de oficinas.
      </div>
      <div class="w-[50px] h-[3px] bg-[#e50113] mt-[36px] mx-auto"></div>
      <div class="mt-[78px] relative">
        <div class="timeline-wrapper relative">
          <!-- 箭头 - 放在外层容器中，使其不随内容滚动 -->
          <div class="arrows-container">
            <img
              alt="quienes somos"
              class="right-arrow absolute right-[38px] top-[975px] cursor-pointer transition-opacity duration-300 z-20"
              :class="{ 'cursor-not-allowed': pageData.isRightEnd }"
              :src="pageData.isRightEnd ? arrowFillGrey : arrowFillRed"
              @click="!pageData.isRightEnd && scrollTimeline('right')"
              loading="lazy"
            />
            <img
              alt="quienes somos"
              class="left-arrow absolute left-[38px] top-[1720px] rotate-180 cursor-pointer transition-opacity duration-300 z-20"
              :class="{ 'cursor-not-allowed': pageData.isLeftEnd }"
              :src="pageData.isLeftEnd ? arrowFillGrey : arrowFillRed"
              @click="!pageData.isLeftEnd && scrollTimeline('left')"
              loading="lazy"
            />
          </div>
          <!-- 时间线内容 - 放在可滚动容器中 -->
          <div
            class="relative h[1800px] pt-[1120px] pb-[650px] pr-[38px] timeline-container"
          >
            <div
              class="w-[2148px] h-[1120px] absolute z-20 top-[680px] timeline-drag custom-cursor"
            ></div>
            <!-- 时间线 -->
            <div class="h-[1px] bg-[#ccc] w-[2148px]"></div>
            <!-- 时间节点 -->
            <div class="flex relative">
              <!-- firstOffice -->
              <div class="relative">
                <!-- 时间点标记 -->
                <div
                  class="w-[21px] h-[21px] bg-[#B3B3B3] rounded-full absolute top-[-10px] left-[140px] z-11"
                ></div>
                <div
                  class="w-[1px] h-[450px] bg-[#CCC] absolute top-[-450px] left-[150px] z-10"
                ></div>
                <!-- 时间图标 -->
                <img
                  loading="lazy"
                  alt="quienes somos"
                  :src="firstOfficeTime"
                  class="w-[604px] absolute top-[-548px] left-[54px] z-10 max-w-[initial]"
                />
                <!-- 办公室图片 -->
                <img
                  loading="lazy"
                  alt="quienes somos"
                  :src="firstOffice"
                  class="w-[713px] h-[485px] absolute top-[-1065px] left-[0px] z-10 max-w-[initial]"
                />
                <!-- 标题 -->
                <div
                  class="text-[30px] leading-[30px] font-medium absolute top-[-1120px] z-10 whitespace-nowrap"
                >
                  Primera oficina
                </div>
              </div>
              <!--firstRenovation  -->
              <div class="absolute left-[433px]">
                <!-- 时间点标记 -->
                <div
                  class="w-[21px] h-[21px] bg-[#B3B3B3] rounded-full absolute top-[-10px] z-11"
                ></div>
                <div
                  class="w-[1px] h-[66px] bg-[#CCC] absolute top-[-76px] left-[10px] z-10"
                ></div>
                <!-- 时间图标 -->
                <img
                  loading="lazy"
                  alt="quienes somos"
                  :src="firstRenovationTime"
                  class="w-[118px] absolute top-[-132px] left-[-48px] z-10 max-w-[initial]"
                />
                <!-- 办公室图片 -->
                <img
                  loading="lazy"
                  alt="quienes somos"
                  :src="firstRenovation"
                  class="w-[366px] absolute top-[-404px] left-[-160px] z-10 max-w-[initial]"
                />
                <!-- 标题 -->
                <div
                  class="text-[30px] leading-[30px] font-medium absolute top-[-452px] left-[-160px] z-10 whitespace-nowrap"
                >
                  Renovación
                </div>
              </div>
              <!-- secondOffice -->
              <div class="absolute top-[0] left-[740px]">
                <div
                  class="w-[21px] h-[21px] bg-[#B3B3B3] rounded-full absolute top-[-10px] left-0 z-11"
                ></div>
                <div
                  class="w-[1px] h-[66px] bg-[#CCC] absolute top-[10px] left-[10px] z-10 max-w-[initial]"
                ></div>
                <div
                  class="text-[30px] leading-[30px] font-medium absolute top-[96px] left-[-5px] whitespace-nowrap"
                >
                  La segunda oficina
                </div>
                <img
                  loading="lazy"
                  alt="quienes somos"
                  :src="secondOffice"
                  class="w-[366px] absolute top-[144px] left-[-5px] max-w-[initial]"
                />
                <img
                  loading="lazy"
                  alt="quienes somos"
                  :src="secondOfficeTime"
                  class="w-[326px] absolute top-[410px] left-[-5px] max-w-[initial]"
                />
              </div>
              <!-- thirdOffice -->
              <div class="absolute top-[0] left-[1132px]">
                <div
                  class="w-[21px] h-[21px] bg-[#B3B3B3] rounded-full absolute top-[-10px] left-0 z-11"
                ></div>
                <div
                  class="w-[1px] h-[66px] bg-[#CCC] absolute top-[10px] left-[10px] z-10 max-w-[initial]"
                ></div>
                <div
                  class="text-[30px] leading-[30px] font-medium absolute top-[96px] left-[-5px] whitespace-nowrap"
                >
                  Tercera oficina
                </div>

                <img
                  loading="lazy"
                  alt="quienes somos"
                  :src="thirdOffice"
                  class="w-[366px] absolute top-[144px] left-[-5px] max-w-[initial]"
                />
                <img
                  loading="lazy"
                  alt="quienes somos"
                  :src="thirdOfficeTime"
                  class="w-[330px] absolute top-[410px] left-[-5px] max-w-[initial]"
                />
              </div>
              <!-- fourthOffice -->
              <div class="absolute top-[0] left-[1528px]">
                <div class="custom-icon absolute top-[-17px] left-0 z-11">
                  <span></span>
                </div>
                <div
                  class="w-[1px] h-[66px] bg-[#e50113] absolute top-[10px] left-[16.5px] z-10 max-w-[initial]"
                ></div>
                <div
                  class="text-[30px] leading-[30px] font-medium absolute top-[96px] left-[-5px] whitespace-nowrap"
                >
                  Cuarta oficina
                </div>

                <img
                  loading="lazy"
                  alt="quienes somos"
                  :src="fourthOffice"
                  class="w-[620px] absolute top-[144px] left-[-5px] max-w-[initial]"
                />
                <img
                  loading="lazy"
                  alt="quienes somos"
                  :src="fourthOfficeTime"
                  class="w-[398px] absolute top-[590px] left-[-5px] max-w-[initial]"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="pt-[120px] px-[38px] text-center">
      <div class="text-[34px] leading-[34px] font-medium">Nuestro equipo</div>
      <div
        class="text-[18px] leading-[22px] text-[#7f7f7f] mt-[16px] px-[130px]"
      >
        <div>
          Ahora tenemos un equipo de 80 personas en Yiwu y hay 6 personas en la
          oficina de Guangzhou, incluyendo 40 personas de servicio al cliente de
          hispanohablante.
        </div>
        <div>
          Ahora se ha convertido en la empresa profesional más grande de Yiwu al
          servicio de los países de habla hispana.
          <br />
          Los siguientes son nuestros Staff Administrativos y sus datos
          personales:
        </div>
      </div>
      <div class="w-[50px] h-[3px] bg-[#e50113] mt-[36px] mx-auto"></div>
      <div class="text-[28px] leading-[28px] mt-[78px] font-medium">
        Dirección Ejecutiva
      </div>
      <n-space class="mt-[78px]" :style="{ gap: '30px 20px' }">
        <div
          v-for="(manager, index) in managementInfo"
          :key="index"
          class="w-[286px] cursor-pointer"
        >
          <div class="relative">
            <img
              loading="lazy"
              :src="manager.image"
              alt="quienes somos"
              class="w-[286px] h-[352px] rounded-[20px]"
            />
            <div
              class="h-[352px] absolute top-0 left-0 text-white py-[18px] pl-[18px] overflow-hidden opacity-0 hover:opacity-100 transition-opacity duration-300 text-[16px] leading-[24px] rounded-[20px]"
              style="background-color: rgba(0, 0, 0, 0.55)"
            >
              <n-scrollbar
                style="text-align: initial"
                trigger="none"
                class="overflow-auto pr-[18px]"
              >
                <div v-html="manager.description"></div>
              </n-scrollbar>
            </div>
          </div>
          <div class="px-[18px] text-[20px] leading-[20px] mt-[24px]">
            {{ manager.name }}
          </div>
          <div
            class="px-[18px] text-[16px] leading-[20px] mt-[18px] text-[#7f7f7f] min-h-[72px]"
          >
            {{ manager.position }}
          </div>
        </div>
      </n-space>
    </div>
    <div class="pt-[78px] pb-[150px] custom-cursor">
      <div class="text-[28px] leading-[28px] text-center font-medium">
        Fotos del equipo
      </div>
      <Carousel
        ref="teamCarousel1"
        :images="imageArr1"
        direction="left"
        :speed="100"
        :autoplay="true"
        :autoPlayWhenVisible="true"
        :gap="0"
        class="mt-[78px]"
        imageWidth="auto"
        imageHeight="238px"
      />
      <Carousel
        ref="teamCarousel2"
        :images="imageArr2"
        direction="right"
        :speed="100"
        :autoplay="true"
        :autoPlayWhenVisible="true"
        :gap="0"
        class="mt-[12px]"
        imageWidth="auto"
        imageHeight="238px"
      />
    </div>
    <div class="overflow-auto text-center custom-cursor">
      <div class="text-[34px] leading-[34px] font-medium">
        Nuestros clientes
      </div>
      <div
        class="text-[18px] leading-[22px] text-[#7f7f7f] mt-[16px] px-[163px]"
      >
        Desde el año 2003, hemos atendido a más de 1.500 clientes. Se extienden
        por toda Latinoamérica y España. Su aprobación hacia nosotros es la
        mayor fuerza impulsora de nuestro crecimiento.
      </div>
      <div class="w-[50px] h-[3px] bg-[#e50113] mt-[36px] mx-auto"></div>
      <Carousel
        ref="clientCarousel1"
        :images="imageArr3"
        direction="left"
        :speed="100"
        :autoplay="true"
        :autoPlayWhenVisible="true"
        :gap="0"
        class="mt-[78px]"
        imageWidth="auto"
        imageHeight="238px"
      />
      <Carousel
        ref="clientCarousel2"
        :images="imageArr4"
        direction="right"
        :speed="100"
        :autoplay="true"
        :autoPlayWhenVisible="true"
        :gap="0"
        class="mt-[12px]"
        imageWidth="auto"
        imageHeight="238px"
      />
      <Carousel
        ref="clientCarousel3"
        :images="imageArr5"
        direction="left"
        :speed="100"
        :autoplay="true"
        :autoPlayWhenVisible="true"
        :gap="0"
        class="mt-[12px]"
        imageWidth="auto"
        imageHeight="238px"
      />
    </div>
    <div class="py-[150px] px-[38px]" id="empleo">
      <div class="w-[1204px] h-[677.25px] rounded-[20px] overflow-hidden">
        <video-you-tube
          :width="1204"
          :height="677.25"
          youtubeId="zHrBAsWg3WM"
          titleCh="与中国的进口和业务"
          poster="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/03/17/99573a79-fe41-4cc3-8107-731a0fc1528c.png"
        ></video-you-tube>
      </div>
      <div
        class="text-[20px] leading-[30px] text-[#7f7f7f] mt-[120px] w-[640px]"
      >
        Somos una empresa internacional con una cartera de clientes en el
        negocio de importaciones que expande también sus propuestas de servicios
        a proyectos de Inversión en China, Sourcing y Oportunidades de Negocios
        entre China para Latinoamérica y todos los mercados de habla hispana en
        el mundo.
      </div>
    </div>
    <div
      class="rounded-[20px] w-full h-[304px] text-[#fff] pt-[49px] text-center"
      :style="{
        backgroundImage: `url(${redBg})`,
        backgroundSize: '100% 100%',
      }"
    >
      <div class="text-[34px] leading-[34px] font-bold">
        Solicita nuestra aseoria
      </div>
      <div class="text-[20px] leading-[20px] mt-[40px]">
        Tenemos los servicios y experiencia que busca
      </div>
      <n-button
        size="large"
        color="#fff"
        text-color="#e50113"
        @click="onWhatsAppClick"
        class="w-[204px] h-[78px] rounded-[500px] mt-[54px] global-contact-btn"
      >
        <span class="text-[18px] leading-[18px] mr-[8px]">Contacto</span>
        <img
          loading="lazy"
          alt="quienes somos"
          class="arrow-icon"
          src="@/assets/icons/quienes-somos/arrow-line.svg"
        />
      </n-button>
    </div>
    <div class="py-[150px] px-[38px] flex items-center">
      <div
        class="flex-shrink-0 pr-[40px] mr-[40px] border-r-1 border-[#E6E6E6]"
      >
        <div class="w-[536px] h-[301px] rounded-[20px] overflow-hidden">
          <video-you-tube
            :width="536"
            :height="301"
            youtubeId="pj-CU1JJ8fc"
            titleCh="我们的故事"
            poster="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/03/18/7ff8355b-159e-4893-9a5d-f43d44af8cce.png"
          ></video-you-tube>
        </div>
      </div>
      <div>
        <div class="text-[34px] leading-[34px] font-medium">
          Nuestra historia
        </div>
        <div class="w-[50px] h-[3px] bg-[#e50113] mt-[54px]"></div>
        <div class="text-[16px] leading-[22px] text-[#7F7F7F] mt-[56px]">
          Nuestra compañía se originó en 1998, la Sra. Lily Dai comenzó a
          comprar productos en el mercado de Yiwu y los vendía en Argentina. En
          el 2003, estableció una oficina en Yiwu y comenzó a ayudar a otros
          importadores a comprar productos en China…
        </div>
      </div>
    </div>
    <div class="pt-[80px] pb-[200px] px-[38px] bg-[#FAFAFA] relative">
      <div class="text-[40px] leading-[48px] font-medium">
        Misión,Visón,Valores
      </div>
      <div class="w-[50px] h-[3px] bg-[#e50113] mt-[36px]"></div>
      <div class="mt-[78px] flex">
        <div class="flex-1">
          <div class="flex">
            <img
              loading="lazy"
              :src="arrowRightBold"
              alt="quienes somos"
              class="w-[8px] mr-[12px]"
            />
            <div class="text-[30px] leading-[30px] font-medium">01</div>
          </div>
          <div class="text-[20px] leading-[20px] font-medium mt-[30px]">
            Nuestra misión
          </div>
          <div class="text-[16px] leading-[20px] text-[#7F7F7F] mt-[26px]">
            Brindar Servicios Corporativos de Excelencia para proyectos de
            importación desde China en el idioma español y abrir oportunidades
            de negocios exitosos para distintos rubros y mercados
            internacionales en el sector B2B.
          </div>
        </div>
        <div class="flex-1 mx-[50px]">
          <div class="flex">
            <img
              loading="lazy"
              :src="arrowRightBold"
              alt="quienes somos"
              class="w-[8px] mr-[12px]"
            />
            <div class="text-[30px] leading-[30px] font-medium">02</div>
          </div>
          <div class="text-[20px] leading-[20px] font-medium mt-[30px]">
            Nuestra visión
          </div>
          <div class="text-[16px] leading-[20px] text-[#7F7F7F] mt-[26px]">
            Ser reconocida en Latinoamérica y todas las comunidades de habla
            hispana como la empresa internacional líder que genera las mejores
            oportunidades de negocios con China basándose en el respeto por el
            cliente, la transparencia y honestidad en todo acuerdo comercial,
            agilidad, innovación y profesionalismo en los servicios que ofrece y
            por su calidez humana y relaciones de confianza a largo plazo.
          </div>
        </div>
        <div class="flex-1">
          <div class="flex">
            <img
              loading="lazy"
              :src="arrowRightBold"
              alt="quienes somos"
              class="w-[8px] mr-[12px]"
            />
            <div class="text-[30px] leading-[30px] font-medium">03</div>
          </div>
          <div class="text-[20px] leading-[20px] font-medium mt-[30px]">
            Valores corporativos
          </div>
          <div class="text-[16px] leading-[20px] text-[#7F7F7F] mt-[26px]">
            <div>
              <span class="font-medium text-[#e50113]">•Transparencia:</span>
              Ofrecemos servicios basados en la confianza que se demuestran con
              los hechos, sistemas de información que verifican nuestros
              procesos de calidad, ética comercial que privilegia la honestidad
              en todas las operaciones y en el diálogo directo con los líderes
              de nuestra compañía.
            </div>
            <div class="mt-[20px]">
              <span class="font-medium text-[#e50113]">•Respeto:</span>
              Valoramos los intereses de nuestros clientes y aliados para
              encontrar acuerdos estratégicos que permiten crear situaciones de
              beneficios mutuo, equilibrados y exitosospara todos los
              involucrados en cada una de nuestras operaciones comerciales.
            </div>
            <div class="mt-[20px]">
              <span class="font-medium text-[#e50113]">•Visión:</span>
              Trabajamos cada día para superar las expectativas de nuestros
              clientes ofreciendo servicios innovadores y analizando las
              condiciones de mercados rentables futuros.
            </div>
            <div class="mt-[20px]">
              <span class="font-medium text-[#e50113]">•Calidad:</span>
              Creemos que las posibilidades de éxitos son posible gracias al
              trabajo realizado y fundamentalmente por la calidad de servicios
              que prestamos donde la satisfacción de nuestros clientes es la
              mejor promoción que tenemos en los mercados.
            </div>
            <div class="mt-[20px]">
              <span class="font-medium text-[#e50113]">•Vínculo:</span>
              Forjamos una empresa con servicios de calidad internacional y
              también respetamos las tradiciones que priorizan en la calidad y
              cuidado de las relaciones humanas como la base del éxito a través
              del tiempo.
            </div>
            <div class="mt-[20px]">
              <span class="font-medium text-[#e50113]">•Confianza:</span>
              Construimos relaciones a largo plazo en todos los negocios y
              acciones que emprendemos. Así logramos inspirar a empleados,
              clientes, socios y todos las personas relacionadas con nuestras
              actividades en seguir depositando su confianza a través del
              tiempo.
            </div>
          </div>
        </div>
      </div>
      <n-button
        size="large"
        color="#e50113"
        text-color="#fff"
        @click="onWhatsAppClick"
        class="absolute left-[78px] bottom-[440px] w-[446px] h-[86px] rounded-[500px] mt-[54px] contactus-btn"
      >
        <span class="text-[18px] leading-[18px] mr-[16px]"
          >¡Necesito un agente ahora!
        </span>
        <img
          loading="lazy"
          alt="quienes somos"
          class="arrow-icon"
          src="@/assets/icons/quienes-somos/arrow-white.svg"
        />
      </n-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import homeIcon from "@/assets/icons/common/home.svg";
import veteran from "@/assets/icons/quienes-somos/veteran.svg";
import branches from "@/assets/icons/quienes-somos/branches.svg";
import multilingual from "@/assets/icons/quienes-somos/multilingual.svg";
import procurementRed from "@/assets/icons/quienes-somos/procurement-red.svg";
import procurementBlack from "@/assets/icons/quienes-somos/procurement-black.svg";
import supplierRed from "@/assets/icons/quienes-somos/supplier-red.svg";
import supplierBlack from "@/assets/icons/quienes-somos/supplier-black.svg";
import validationRed from "@/assets/icons/quienes-somos/validation-red.svg";
import validationBlack from "@/assets/icons/quienes-somos/validation-black.svg";
import translationRed from "@/assets/icons/quienes-somos/translation-red.svg";
import translationBlack from "@/assets/icons/quienes-somos/translation-black.svg";
import agentRed from "@/assets/icons/quienes-somos/agent-red.svg";
import agentBlack from "@/assets/icons/quienes-somos/agent-black.svg";
import inspectionRed from "@/assets/icons/quienes-somos/inspection-red.svg";
import inspectionBlack from "@/assets/icons/quienes-somos/inspection-black.svg";
import consolidationRed from "@/assets/icons/quienes-somos/consolidation-red.svg";
import consolidationBlack from "@/assets/icons/quienes-somos/consolidation-black.svg";
import designRed from "@/assets/icons/quienes-somos/design-red.svg";
import designBlack from "@/assets/icons/quienes-somos/design-black.svg";
import firstOffice from "@/assets/icons/quienes-somos/first-office.png";
import firstRenovation from "@/assets/icons/quienes-somos/first-renovation.png";
import secondOffice from "@/assets/icons/quienes-somos/second-office.png";
import thirdOffice from "@/assets/icons/quienes-somos/third-office.png";
import fourthOffice from "@/assets/icons/quienes-somos/fourth-office.png";
import firstOfficeTime from "@/assets/icons/quienes-somos/first-office-time.svg";
import firstRenovationTime from "@/assets/icons/quienes-somos/first-renovation-time.svg";
import secondOfficeTime from "@/assets/icons/quienes-somos/second-office-time.svg";
import thirdOfficeTime from "@/assets/icons/quienes-somos/third-office-time.svg";
import fourthOfficeTime from "@/assets/icons/quienes-somos/fourth-office-time.svg";
import manager1 from "@/assets/icons/quienes-somos/manager1.jpg";
import manager2 from "@/assets/icons/quienes-somos/manager2.jpg";
import manager3 from "@/assets/icons/quienes-somos/manager3.jpg";
import manager4 from "@/assets/icons/quienes-somos/manager4.jpg";
import manager5 from "@/assets/icons/quienes-somos/manager5.jpg";
import manager6 from "@/assets/icons/quienes-somos/manager6.jpg";
import manager7 from "@/assets/icons/quienes-somos/manager7.jpg";
import manager8 from "@/assets/icons/quienes-somos/manager8.jpg";
import team1 from "@/assets/icons/quienes-somos/team1.png";
import team2 from "@/assets/icons/quienes-somos/team2.png";
import team3 from "@/assets/icons/quienes-somos/team3.png";
import team4 from "@/assets/icons/quienes-somos/team4.png";
import team5 from "@/assets/icons/quienes-somos/team5.png";
import team6 from "@/assets/icons/quienes-somos/team6.png";
import team7 from "@/assets/icons/quienes-somos/team7.png";
import team8 from "@/assets/icons/quienes-somos/team8.png";
import team9 from "@/assets/icons/quienes-somos/team9.png";
import team10 from "@/assets/icons/quienes-somos/team10.png";
import team11 from "@/assets/icons/quienes-somos/team11.png";
import team12 from "@/assets/icons/quienes-somos/team12.png";
import customer1 from "@/assets/icons/quienes-somos/customer1.png";
import customer2 from "@/assets/icons/quienes-somos/customer2.png";
import customer3 from "@/assets/icons/quienes-somos/customer3.png";
import customer4 from "@/assets/icons/quienes-somos/customer4.png";
import customer5 from "@/assets/icons/quienes-somos/customer5.png";
import customer6 from "@/assets/icons/quienes-somos/customer6.png";
import customer7 from "@/assets/icons/quienes-somos/customer7.png";
import customer8 from "@/assets/icons/quienes-somos/customer8.png";
import customer9 from "@/assets/icons/quienes-somos/customer9.png";
import customer10 from "@/assets/icons/quienes-somos/customer10.png";
import customer11 from "@/assets/icons/quienes-somos/customer11.png";
import customer12 from "@/assets/icons/quienes-somos/customer12.png";
import customer13 from "@/assets/icons/quienes-somos/customer13.png";
import customer14 from "@/assets/icons/quienes-somos/customer14.png";
import customer15 from "@/assets/icons/quienes-somos/customer15.png";
import customer16 from "@/assets/icons/quienes-somos/customer16.png";
import customer17 from "@/assets/icons/quienes-somos/customer17.png";
import customer18 from "@/assets/icons/quienes-somos/customer18.png";
import customer19 from "@/assets/icons/quienes-somos/customer19.png";
import customer20 from "@/assets/icons/quienes-somos/customer20.png";
import customer21 from "@/assets/icons/quienes-somos/customer21.png";
import redBg from "@/assets/icons/quienes-somos/red-bg.png";
import arrowRightBold from "@/assets/icons/quienes-somos/arrow-right-bold.svg";
import arrowFillRed from "@/assets/icons/quienes-somos/arrow-fill-red.svg";
import arrowFillGrey from "@/assets/icons/quienes-somos/arrow-fill-grey.svg";

useHead({
  title: "Quienes somos_Introducción al negocio de la empresa-Chilat",
  meta: [
    {
      name: "description",
      content:
        "Somos el agente de compras y abastecimiento más grande de China dedicado a servir a importadores de habla hispana. Ofrecemos a nuestros clientes un conjunto de soluciones para todas las importaciones desde China.",
    },
  ],
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/quienes-somos/`,
    },
    {
      rel: "alternate",
      media: "only screen and (max-width: 640px)",
      href: `https://www.chilat.com/h5/quienes-somos/`,
    },
  ],
});

const breadcrumbItems = [
  { link: "/", icon: homeIcon, alt: "home" },
  { text: "Nosotros" },
  { link: "/quienes-somos/", text: "Quiénes somos" },
];

const chilatInfo = [
  {
    icon: veteran,
    title: "22 años de experiencia",
  },
  {
    icon: multilingual,
    title: "Equipo de más de 40 hispanoparlantes",
  },
  {
    icon: branches,
    title: "Oficinas en Yiwu y Guangzhou",
  },
];

const serviceInfo = [
  {
    icon: procurementRed,
    activatedIcon: procurementBlack,
    title: "Asesoría de compras",
    path: "/servicios#asesoria-de-compras",
  },
  {
    icon: supplierRed,
    activatedIcon: supplierBlack,
    title: "Búsqueda de proveedores y productos",
    path: "/servicios#busqueda-de-proveedores-y-productos",
  },
  {
    icon: validationRed,
    activatedIcon: validationBlack,
    title: "Validación de fabricantes",
    path: "/servicios#validacion-de-fabricantes",
  },
  {
    icon: translationRed,
    activatedIcon: translationBlack,
    title: "Servicio de traducción al español y asistente de viaje de negocios",
    path: "/servicios#viajes-de-negocios-a-china",
  },
  {
    icon: agentRed,
    activatedIcon: agentBlack,
    title: "Agente de compras para los mercados",
    path: "/viajar-a-china",
  },
  {
    icon: inspectionRed,
    activatedIcon: inspectionBlack,
    title: "Control de calidad e Inspección de mercancías",
    path: "/servicios#control-de-calidad",
  },
  {
    icon: consolidationRed,
    activatedIcon: consolidationBlack,
    title: "Consolidación de mercaderías y logística",
    path: "/consolidacion-de-cargas-y-logistica",
  },
  {
    icon: designRed,
    activatedIcon: designBlack,
    title: "Diseños e imágenes de producto",
    path: "/servicios#fotos-del-producto-y-catalogo",
  },
];

const supportInfo = [
  "Con más de 20000 recursos de proveedores, podemos ayudarle a comparar fábricas para obtener precios más competitivos.",
  "Cuando no se encuentre en China, podemos ayudarle a encontrar los productos que necesita y ofrecerle una cotización en 48 horas.",
  "Podemos ayudarle a consolidar productos de múltiples proveedores en un solo contenedor.",
  "Comunicamos con distintos proveedores en espanol, y tambien supervisamos su pedido.",
  "Podemos ayudarle en el mercado de Yiwu, Guangzhou y mercados especializados a sus alrededores, con compra directa en fábrica.",
];

const managementInfo = [
  {
    image: manager2,
    name: "Wang Min 汪敏 (Mindy)",
    position:
      "CEO y Presidente del Grupo Chilat, Gerente General en MingZhan Trade Import & Export Co.,LTD.",
    description:
      "<div>Nació en Shanghái (China) en 1980. Es licenciada por la Universidad de Macquarie (Sídney, Australia) y habla tres idiomas: mandarín, inglés y español.</div><div>Desde 2002 trabaja en el negocio familiar como asistente de comercio exterior. Su negocio familiar importaba desde Argentina desde 1990. Es la única hija de la familia. Por eso decidió trabajar en el negocio familiar. La empresa familiar era importadora y agente de compras.</div><div>En 2011, ella y su esposo fundaron la empresa Mingzhan Imp. & Exp.</div><div>En 2016, fundó Chilat como una marca de servicio a clientes latinoamericanos. La empresa se centra en el mercado latinoamericano. La marca Chilat —China Latinoamérica— está registrada.</div><div>Cuenta con 23 años de experiencia como consultora especialista en comercio exterior. Su empresa ha ayudado a más de 1000 importadores de Latinoamérica y, como era importadora, entiende mejor que nadie.</div>",
  },
  {
    image: manager1,
    name: "Renyi 任毅 (Andy)",
    position:
      "CEO y Presidente del Grupo Chilat, Director General MingZhan Trade Import & Export Co.,LTD.",
    description:
      "<div>Estudió Economía Internacional en Sídney (Australia), donde residió durante años. </div><div>Tras su experiencia internacional, regresó a China para trabajar con su esposa en un nuevo concepto de trading y ampliar su visión comercial a nuevos desarrollos en el país, que actualmente tiene un gran potencial. Le gusta la cocina internacional y viajar.</div>",
  },
  {
    image: manager3,
    name: "Irene",
    position: "Gerente de cliente",
    description:
      "<div>Se llama Irene, trabaja en Chilat desde hace 16 años y tiene mucha experiencia en comercio exterior.</div><div>Estudió español durante 3 años en la universidad y luego empezó a trabajar en esta empresa.</div><div>Es muy simpática y tiene mucha paciencia.</div>",
  },
  {
    image: manager4,
    name: "Elisa Wang",
    position: "Gerente de cliente exterior de Chilat",
    description:
      "<div>Se llama Elisa, trabaja en Chilat desde hace 16 años.</div><div>Ayuda a muchos clientes a importar de China y a ampliar su negocio gracias a su amplia experiencia en importación y exportación.</div><div>Es una profesional apasionada por su trabajo, responsable con los clientes y con una actitud de servicio eficiente y paciente.</div>",
  },
  {
    image: manager5,
    name: "Tania",
    position: "Gerente de cliente",
    description:
      "<div>Se llama Tania y ha trabajado con Chilat durante 8 años.</div><div>Ha ayudado a muchos clientes de más de 13 países latinoamericanos a importar desde China y ampliar sus negocios. Tiene mucha experiencia y conocimientos sobre la importación de cada país.</div><div>Trabaja de manera eficiente y con mucha pasión.</div>",
  },
  {
    image: manager6,
    name: "Iris",
    position: "Gerente de cliente",
    description:
      "<div>Se llama Iris y trabaja en Chilat desde hace 8 años.</div><div>Cuenta con muchos años de experiencia en importación y exportación, y ha ayudado a clientes de países latinoamericanos a importar productos de China.</div><div>Es una apasionada de su trabajo, responsable con sus clientes y muy eficiente y paciente.</div>",
  },
  {
    image: manager7,
    name: "Celia",
    position: "Gerente de cliente",
    description:
      "<div>Se llama Celia. Ha trabajado con Chilat durante 8 años.</div><div>Su pronunciación es muy similar a la de «seria», pero también refleja que ella muy serio y responsable a la hora de gestionar todos los pedidos.</div><div>Trata sinceramente a cada cliente, pero en realidad el personaje es muy cálido y alegre.</div><div>Espera conocer a muchos amigos en Latinoamérica.</div>",
  },
  {
    image: manager8,
    name: "Karmen",
    position: "Gerente de cliente",
    description:
      "<div>Se llama Karmen Ye, ha vivido en Ecuador con toda su familia durante 8 años, ha estudiado y trabajado.</div><div>Es capaz de entender las necesidades de los demás con rapidez.</div><div>Es una chica muy simpática a la que le gusta viajar y hacer amigos, y es responsable y paciente en el trabajo.</div>",
  },
];

const imageArr1 = [team1, team2, team3, team4, team5, team6];
const imageArr2 = [team7, team8, team9, team10, team11, team12];
const imageArr3 = [customer1, customer2, customer3, customer4];
const imageArr4 = [
  customer5,
  customer6,
  customer7,
  customer8,
  customer9,
  customer10,
  customer11,
  customer12,
];
const imageArr5 = [
  customer13,
  customer14,
  customer15,
  customer16,
  customer17,
  customer18,
  customer19,
  customer20,
  customer21,
];

const pageData = reactive({
  serviceIndex: -1,
  // 添加时间线滚动状态控制
  isLeftEnd: true, // 初始时在最左侧，左箭头禁用
  isRightEnd: false,
  // 添加拖拽相关状态
  isDragging: false,
  startX: 0,
  scrollLeft: 0,
});

// 轮播图引用
const teamCarousel1 = ref(null);
const teamCarousel2 = ref(null);
const clientCarousel1 = ref(null);
const clientCarousel2 = ref(null);
const clientCarousel3 = ref(null);

// 时间线滚动方法
const scrollTimeline = (direction: "left" | "right") => {
  const container = document.querySelector(".timeline-container");
  if (!container) return;

  const scrollAmount = 600; // 每次滚动的距离
  const currentScroll = container.scrollLeft;
  const newScroll =
    direction === "left"
      ? currentScroll - scrollAmount
      : currentScroll + scrollAmount;

  // 使用平滑滚动效果
  container.scrollTo({
    left: newScroll,
    behavior: "smooth",
  });
};

// 拖拽开始事件处理函数
const handleMouseDown = (e: any) => {
  const container = document.querySelector(
    ".timeline-container"
  ) as HTMLElement;
  if (!container) return;

  pageData.isDragging = true;
  pageData.startX = e.pageX - container.offsetLeft;
  pageData.scrollLeft = container.scrollLeft;
};

// 拖拽移动事件处理函数
const handleMouseMove = (e: any) => {
  if (!pageData.isDragging) return;

  const container = document.querySelector(
    ".timeline-container"
  ) as HTMLElement;
  if (!container) return;

  const x = e.pageX - container.offsetLeft;
  const walk = (x - pageData.startX) * 2; // 滚动速度系数
  container.scrollLeft = pageData.scrollLeft - walk;

  // 更新箭头状态
  checkScrollPosition(container);

  e.preventDefault(); // 防止选中文本等默认行为
};

// 拖拽结束事件处理函数
const handleMouseUp = () => {
  pageData.isDragging = false;
};

// 组件挂载后添加事件监听器
onMounted(() => {
  const container = document.querySelector(
    ".timeline-container"
  ) as HTMLElement;
  const dragArea = document.querySelector(".timeline-drag") as HTMLElement;
  if (!container || !dragArea) return;

  // 初始检查
  checkScrollPosition(container);

  // 监听滚动事件
  container.addEventListener("scroll", () => {
    checkScrollPosition(container);
  });

  // 添加鼠标拖拽事件监听
  dragArea.addEventListener("mousedown", handleMouseDown as EventListener);
  document.addEventListener("mousemove", handleMouseMove as EventListener);
  document.addEventListener("mouseup", handleMouseUp as EventListener);
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener("mousemove", handleMouseMove);
  document.removeEventListener("mouseup", handleMouseUp);
  document.removeEventListener("mouseleave", handleMouseUp);
});

// 检查滚动位置并更新箭头状态
const checkScrollPosition = (container: HTMLElement) => {
  // 检查是否滚动到最左侧
  pageData.isLeftEnd = container.scrollLeft <= 0;

  // 检查是否滚动到最右侧
  const maxScrollLeft = container.scrollWidth - container.clientWidth;
  pageData.isRightEnd = Math.abs(container.scrollLeft - maxScrollLeft) < 5; // 允许5px的误差
};
</script>
<style scoped lang="scss">
.page-header {
  width: calc(100% - 76px);
  height: 367px;
  margin: 0 auto;
  position: relative;
  object-fit: cover;
  background-size: 100%100%;
  background-image: url("@/assets/icons/quienes-somos/about-us-bg.png");
  background-repeat: no-repeat;
}

.support-btn {
  border: 0.02rem solid #e50113;
  color: #e50113;
  background: transparent;
  transition: background-color 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    color 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    box-shadow 0.4s cubic-bezier(0.4, 0, 0.2, 1); // 柔和缓动
  &:hover {
    background: #e50113;
    color: #fff;
  }
}

.timeline-wrapper {
  position: relative;
  width: 100%;
}

.arrows-container {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 30;
}

.arrows-container img {
  pointer-events: auto;
}

.timeline-container {
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  white-space: nowrap;
  width: 100%;
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}
.custom-cursor {
  cursor: url("@/assets/icons/quienes-somos/cursor.ico"), auto !important;
}

.contactus-btn {
  position: relative;
  overflow: hidden;
  background-color: #e50113;
  color: #fff;
  transition: all 0.8s cubic-bezier(0.25, 1, 0.5, 1);

  .arrow-icon {
    transition: transform 0.8s cubic-bezier(0.25, 1, 0.5, 1),
      opacity 0.8s ease-in-out;
    opacity: 1;
  }

  &:hover {
    background-color: #fff;
    border: 1px solid #e50113;
    color: #e50113;
    transition: all 0.8s cubic-bezier(0.25, 1, 0.5, 1);

    .arrow-icon {
      content: url("@/assets/icons/quienes-somos/arrow-red.svg");
      transform: translateX(8px) scale(1.4);
    }
  }

  &:not(:hover) {
    transition: all 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);

    .arrow-icon {
      transform: translateX(0) scale(1);
      opacity: 1;
    }
  }
}

:deep(.n-scrollbar-rail__scrollbar) {
  background-color: #f2f2f2 !important;
}

.custom-icon {
  position: relative;
  width: 33px;
  height: 33px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
}

/* 内圆 */
.custom-icon::after {
  content: "";
  position: absolute;
  width: 21px;
  height: 21px;
  background-color: #e50113;
  border-radius: 50%;
  top: 6px;
  left: 50%;
  transform: translateX(-50%);
}

/* 外圆 */
.custom-icon span {
  position: absolute;
  width: 33px;
  height: 33px;
  border: 1px solid #e50113;
  border-radius: 50%;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}
</style>
