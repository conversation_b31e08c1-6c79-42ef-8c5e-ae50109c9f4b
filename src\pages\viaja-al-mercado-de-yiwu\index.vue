<template>
  <div class="w-[1280px] mx-auto bg-white">
    <div class="page-header text-[#fff] overflow-auto px-[64px] py-[24px]">
      <div class="absolute top-0 left-0 w-full h-full bg-black opacity-45 z-[1]"></div>
      <div class="relative z-1">
        <div class="flex items-center gap-[10px] ml-[-16px]">
          <img
            src="@/assets/icons/viaja-al-mercado-de-yiwu/earth.svg"
            alt="viaja al mercado de yiwu"
            class="w-[36px] h-[36px]"
            loading="lazy"
          />
          <div class="text-[16px] leading-[16px]">Descubre el Paraíso de las Importaciones</div>
        </div>
        <div class="text-[76px] leading-[94px] font-medium mt-[18px]">
          ¡Viaja con<br />
          Chilat al Mercado<br />
          de <PERSON>wu!
        </div>
        <div
          @click="onWhatsAppClick"
          class="inline-block py-[24px] px-[53px] text-center cursor-pointer rounded-[50px] border-[1px] border-[#fff] text-[20px] leading-[20px] mt-[24px] hover:bg-[#fff] hover:text-[#e50113] transition-all duration-300"
        >
          Chatear ahora
        </div>
      </div>
    </div>
    <div class="py-[150px] px-[38px] text-center">
      <div class="text-[34px] leading-[34px] font-medium">¿Qué es el Mercado de Yiwu?</div>
      <div class="text-[18px] leading-[22px] text-[#7F7F7F] mt-[16px]">
        El centro mayorista más grande del mundo, con más de 75,000 puestos y más de 2.1 millones de productos.<br />
        Aquí encuentras de todo: desde juguetes, decoración, papelería, artículos para el hogar, hasta productos de
        temporada y más.
      </div>
      <div class="w-[50px] h-[3px] bg-[#e50113] mt-[36px] mx-auto"></div>
      <div class="flex justify-between mt-[78px]">
        <div
          v-for="(item, index) in YiwuMarket"
          :key="index"
          class="w-[374px] flex flex-col items-center gap-[20px] text-center"
        >
          <img :src="item.icon" alt="viaja al mercado de yiwu" class="w-[64px] h-[64px]" loading="lazy" />
          <div class="text-[28px] leading-[28px] font-medium">
            {{ item.title }}
          </div>
          <div class="text-[20px] leading-[22px]">
            {{ item.subTitle }}
          </div>
        </div>
      </div>
    </div>
    <div
      class="rounded-[20px] w-full h-[304px] text-[#fff] pt-[30px] text-center"
      :style="{
        backgroundImage: `url(${redBg})`,
        backgroundSize: '100% 100%',
      }"
    >
      <div class="text-[34px] leading-[40px] font-medium">¿Vendes al por mayor?</div>
      <div class="text-[20px] leading-[20px] mt-[40px]">Aquí es tu mejor fuente de productos al mejor precio.</div>
      <n-button
        size="large"
        color="#fff"
        text-color="#e50113"
        @click="onWhatsAppClick"
        class="w-[230px] h-[78px] rounded-[500px] mt-[44px] global-contact-btn"
      >
        <span class="text-[18px] leading-[18px] mr-[8px]">Chatear ahora</span>
        <img
          loading="lazy"
          alt="viaja al mercado de yiwu"
          class="arrow-icon"
          src="@/assets/icons/quienes-somos/arrow-line.svg"
        />
      </n-button>
    </div>
    <div class="w-full px-[38px] py-[150px]">
      <div class="text-[34px] leading-[34px] font-medium text-center">¿Qué incluye nuestro servicio?</div>
      <div class="text-[18px] leading-[22px] mt-[16px] text-[#7F7F7F] text-center">
        Todo el proceso, desde el primer día de tu viaje hasta que recibes la mercancía, está en buenas manos.
      </div>
      <div class="w-[50px] h-[3px] bg-[#e50113] mt-[36px] mx-auto"></div>
      <div class="flex gap-[60px] mt-[78px] pr-[80px] pl-[54px]">
        <div class="flex-1 flex justify-center gap-[34px]">
          <Transition name="fade-slide" mode="out-in">
            <div :key="pageData.activeInclusions.title" class="flex justify-center gap-[34px] w-full">
              <div
                v-for="(item, index) in pageData.activeInclusions.children"
                :key="index"
                class="flex-1 max-w-[340px]"
              >
                <img
                  loading="lazy"
                  :src="item.icon"
                  alt="viaja al mercado de yiwu"
                  class="rounded-tl-[80px] rounded-tr-[20px] rounded-br-[80px] rounded-bl-[1px]"
                />
                <div class="text-[20px] leading-[22px] font-medium mt-[30px]">
                  {{ item.title }}
                </div>
                <div
                  class="text-[#7f7f7f] mt-[12px]"
                  :class="
                    pageData.activeInclusions.children.length === 1
                      ? 'text-[16px] leading-[32px]'
                      : 'text-[16px] leading-[20px]'
                  "
                >
                  {{ item.desc }}
                </div>
              </div>
            </div>
          </Transition>
        </div>
        <div class="flex flex-col w-[538px] cursor-pointer">
          <div
            v-for="(item, index) in consolidationInclusions"
            :key="index"
            class="flex gap-[54px] px-[20px] py-[24px] border-b-1 border-[#F2F2F2] transition-colors duration-300"
            @mouseenter="handleServiceHover(item, index)"
          >
            <div
              class="text-[16px] leading-[16px] mt-[10px] transition-colors duration-300"
              :class="pageData.activeInclusions.title === item.title ? 'text-[#e50113]' : 'text-[#7F7F7F]'"
            >
              0{{ index + 1 }}
            </div>
            <div
              class="text-[18px] leading-[27px] transition-colors duration-300"
              :class="pageData.activeInclusions.title === item.title ? 'text-[#e50113]' : 'text-[#333]'"
            >
              {{ item.title }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="w-full px-[38px] pb-[150px]">
      <div class="text-[34px] leading-[34px] font-medium text-center">¿Quién debería venir?</div>
      <div class="text-[18px] leading-[22px] mt-[16px] text-[#7F7F7F] text-center">
        ¿Compras en Alibaba? En Yiwu puedes ver, tocar y negociar cara a cara, con nuestra asistencia.
      </div>
      <div class="w-[50px] h-[3px] bg-[#e50113] mt-[36px] mx-auto"></div>
      <div class="flex justify-between flex-wrap mt-[78px] gap-y-[80px]">
        <div
          v-for="(item, index) in WhoShouldCome"
          :key="index"
          class="w-[572px] flex flex-col items-center gap-y-[16px]"
          :class="index === WhoShouldCome.length - 1 ? 'mx-auto' : ''"
        >
          <img :src="item.icon" alt="viaja al mercado de yiwu" class="w-[58px] h-[58px]" loading="lazy" />
          <div class="text-[20px] leading-[22px] text-center px-[36px]">
            {{ item.title }}
          </div>
        </div>
      </div>
    </div>
    <div class="h-[666px] px-[90px] pt-[80px] bg-[#F7F7F7] relative overflow-hidden">
      <div class="w-[1068px] h-[1068px] rounded-full bg-white absolute top-[-124px] left-[274px]"></div>
      <div class="relative z-2">
        <div class="text-[34px] leading-[34px] font-medium text-center">Chilat: Tu socio confiable en Yiwu</div>
        <div class="text-[18px] leading-[22px] mt-[16px] text-center text-[#7F7F7F]">
          Miles de importadores latinoamericanos ya están ganando más con nosotros. ¿Te unirás?
        </div>
        <div class="w-[50px] h-[3px] bg-[#e50113] mt-[36px] mx-auto"></div>
        <div class="flex mt-[78px] gap-[100px]">
          <img
            class="w-[542px] h-[300px] rounded-tl-[80px] rounded-tr-[2px] rounded-br-[80px] rounded-bl-[20px]"
            loading="lazy"
            alt="viaja al mercado de yiwu"
            src="@/assets/icons/viaja-al-mercado-de-yiwu/agent.jpg"
          />
          <div class="flex flex-col gap-[70px] mt-[12px]">
            <div v-for="(info, index) in chilatInfo" :key="index" class="flex items-center gap-[18px]">
              <img loading="lazy" :src="info.icon" alt="viaja al mercado de yiwu" class="w-[34px]" />
              <div class="text-[24px] leading-[24px]">
                {{ info.title }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="rounded-[20px] w-full h-[304px] text-[#fff] pt-[50px] text-center my-[150px]"
      :style="{
        backgroundImage: `url(${redBg})`,
        backgroundSize: '100% 100%',
      }"
    >
      <div class="text-[34px] leading-[34px] font-medium">Contacto rápido</div>
      <div class="text-[20px] leading-[20px] mt-[40px]">
        Haz clic abajo y cuéntanos qué productos buscas. ¡Nosotros te ayudamos a encontrarlos!
      </div>
      <n-button
        size="large"
        color="#fff"
        text-color="#e50113"
        @click="onWhatsAppClick"
        class="w-[262px] h-[78px] rounded-[500px] mt-[44px] global-contact-btn"
      >
        <span class="text-[18px] leading-[18px] mr-[8px]">Llamar ahora</span>
        <img
          loading="lazy"
          alt="viaja al mercado de yiwu"
          class="arrow-icon"
          src="@/assets/icons/quienes-somos/arrow-line.svg"
        />
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import redBg from "@/assets/icons/quienes-somos/red-bg.png";
import stalls from "@/assets/icons/viaja-al-mercado-de-yiwu/stalls.svg";
import stock from "@/assets/icons/viaja-al-mercado-de-yiwu/stock.svg";
import exports from "@/assets/icons/viaja-al-mercado-de-yiwu/exports.svg";
import veteran from "@/assets/icons/quienes-somos/veteran.svg";
import branches from "@/assets/icons/quienes-somos/branches.svg";
import multilingual from "@/assets/icons/quienes-somos/multilingual.svg";

import news from "@/assets/icons/viaja-al-mercado-de-yiwu/news.jpg";
import accompany from "@/assets/icons/viaja-al-mercado-de-yiwu/accompany.jpg";
import communication from "@/assets/icons/viaja-al-mercado-de-yiwu/communication.jpg";
import verify from "@/assets/icons/viaja-al-mercado-de-yiwu/verify.jpg";
import streamline from "@/assets/icons/viaja-al-mercado-de-yiwu/streamline.jpg";
import personalization from "@/assets/icons/viaja-al-mercado-de-yiwu/personalization.jpg";
import explorers from "@/assets/icons/viaja-al-mercado-de-yiwu/explorers.svg";
import buyers from "@/assets/icons/viaja-al-mercado-de-yiwu/buyers.svg";
import wholesalers from "@/assets/icons/viaja-al-mercado-de-yiwu/wholesalers.svg";
import innovators from "@/assets/icons/viaja-al-mercado-de-yiwu/innovators.svg";
import threshold from "@/assets/icons/viaja-al-mercado-de-yiwu/threshold.svg";

useHead({
  title: "Cómo llegar al mercado mayorista de Yiwu en China-Chilat",
  meta: [
    {
      name: "description",
      content:
        "Chilat le ayuda a encontrar todo, desde juguetes, decoración del hogar, papelería, artículos domésticos hasta productos de temporada en el mercado de Yiwu.",
    },
  ],
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/viaja-al-mercado-de-yiwu/`,
    },
    {
      rel: "alternate",
      media: "only screen and (max-width: 640px)",
      href: `https://www.chilat.com/h5/viaja-al-mercado-de-yiwu/`,
    },
  ],
});

const YiwuMarket = [
  {
    icon: stalls,
    title: "75K+",
    subTitle: "Puestos comerciales",
  },
  {
    icon: stock,
    title: "2.1M+",
    subTitle: "Productos disponibles",
  },
  {
    icon: exports,
    title: "219",
    subTitle: "Países de exportación",
  },
];

const WhoShouldCome = [
  {
    icon: explorers,
    title: "Importadores primerizos interesados en explorar el mercado chino",
  },
  {
    icon: buyers,
    title: "Empresarios que desean negociar directamente con proveedores chinos",
  },
  {
    icon: wholesalers,
    title: "Compradores con planes de importación a gran escala",
    desc: "Descubre las últimas tendencias del mercado",
  },
  {
    icon: innovators,
    title: "Emprendedores que buscan desarrollar nuevas categorías o alianzas sólidas",
  },
  {
    icon: threshold,
    title: "Compra mínima sugerida: USD 50,000",
  },
];

const chilatInfo = [
  {
    icon: veteran,
    title: "22 años de experiencia",
  },
  {
    icon: multilingual,
    title: "Servicio 100% en español",
  },
  {
    icon: branches,
    title: "Equipo propio en China",
  },
];

const consolidationInclusions = [
  {
    title: "Acompañamiento en tu idioma",
    children: [
      {
        icon: news,
        title: "Traductor español-chino en todo momento",
        desc: "Nuestro equipo habla español y te acompaña en todo momento.",
      },
    ],
  },
  {
    title: "Agenda personalizada",
    children: [
      {
        icon: accompany,
        title: "Sugerencias personalizadas según lo que vendes",
        desc: "Te ayudamos a planear tu viaje, elegir productos y agendar reuniones clave.",
      },
    ],
  },
  {
    title: "Negociación con proveedores",
    children: [
      {
        icon: communication,
        title: "Apoyo en toma de decisiones y cierre de pedidos",
        desc: "Te apoyamos con cotizaciones, selección de productos y gestión de pedidos.",
      },
    ],
  },
  {
    title: "Recepción de mercancías y inspección",
    children: [
      {
        icon: verify,
        title: "Supervisión de calidad",
        desc: "Desde la producción hasta el envío, control de calidad en todo el proceso para garantizar que cada producto cumpla con los estándares.",
      },
    ],
  },
  {
    title: "Consolidación y logística",
    children: [
      {
        icon: streamline,
        title: "Consolidación de compras",
        desc: "Juntamos todos tus productos, revisamos la calidad y organizamos el envío a tu país.",
      },
    ],
  },
  {
    title: "Asesoría en trámites de importación",
    children: [
      {
        icon: personalization,
        title: "Asesoría completa en trámites aduaneros para evitar problemas en la importación",
        desc: "Te guiamos en toda la documentación: facturas, packing list y más, para que importes sin complicaciones.",
      },
    ],
  },
];

const pageData = reactive({
  activeInclusions: consolidationInclusions[0],
});

const handleServiceHover = (item: any, index: number) => {
  pageData.activeInclusions = item;
};
</script>
<style scoped lang="scss">
.page-header {
  width: 1280px;
  height: 538px;
  margin: 0 auto;
  position: relative;
  object-fit: cover;
  background-size: 100%100%;
  background-image: url("@/assets/icons/viaja-al-mercado-de-yiwu/header-bg.jpg");
  background-repeat: no-repeat;
}

.guide-wrapper {
  &:hover {
    color: #e50113;
    img {
      content: url("@/assets/icons/viaja-al-mercado-de-yiwu/guide-active.svg");
    }
  }
}
.video-wrapper {
  position: relative;
  &:hover {
    color: #25d366;
    img {
      content: url("@/assets/icons/viaja-al-mercado-de-yiwu/video-active.svg");
    }
  }
  &::after {
    content: "";
    position: absolute;
    top: 50%;
    left: -38px;
    transform: translateY(-50%);
    width: 1px;
    height: 110px;
    background-color: #d9d9d9;
  }
}

.whatsapp-wrapper {
  position: relative;
  &:hover {
    color: #25d366;
    img {
      content: url("@/assets/icons/common/whatsapp-highlight.svg");
    }
  }
  &::after {
    content: "";
    position: absolute;
    top: 50%;
    left: -38px;
    transform: translateY(-50%);
    width: 1px;
    height: 110px;
    background-color: #d9d9d9;
  }
}

// 过渡动画样式
.fade-slide-enter-active {
  transition: opacity 0.3s ease-in-out;
}

.fade-slide-leave-active {
  transition: none;
}

.fade-slide-enter-from {
  opacity: 0;
}

.fade-slide-leave-to {
  opacity: 0;
}

.fade-slide-enter-to,
.fade-slide-leave-from {
  opacity: 1;
}

// 图片切换动画
.image-fade-enter-active {
  transition: opacity 0.3s ease-in-out;
}

.image-fade-leave-active {
  transition: none;
}

.image-fade-enter-from {
  opacity: 0;
}

.image-fade-leave-to {
  opacity: 0;
}

.image-fade-enter-to,
.image-fade-leave-from {
  opacity: 1;
}
</style>
